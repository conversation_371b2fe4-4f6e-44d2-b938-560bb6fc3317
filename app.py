import streamlit as st
import warnings
import logging
import pandas as pd

# Suppress Streamlit's internal coroutine warning
warnings.filterwarnings("ignore", message=".*coroutine 'expire_cache' was never awaited.*", category=RuntimeWarning)

# Set pandas option to suppress silent downcasting warning
pd.set_option('future.no_silent_downcasting', True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Page configuration - MUST be the first Streamlit command
st.set_page_config(
    page_title="JerryVision",
    page_icon="assets/favicon.png",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Import UI modules
from ui.sidebar import show_data_overview_sidebar
from ui.pages.raw_data import show_raw_data
from ui.pages.resampled_data import show_resampled_data
from ui.pages.experimental import show_experimental
from ui.pages.buckets import show_buckets
from ui.pages.insights import show_insights

from ui.data_cache import load_all_data_once

# Main app
def main():
    try:
        # Load all data once at startup
        load_all_data_once()
        
        # Sidebar
        show_data_overview_sidebar()
        
        # Main content
        st.title("🎯 JerryVision")
        
        
        # Navigation tabs
        tab1, tab2, tab3, tab4, tab5 = st.tabs([
            "📊 Resampled Data",
            "🧪 Experimental",
            "📈 Raw Data",
            "🪣 Buckets",
            "💡 Insights"
        ])
        
        with tab1:
            show_resampled_data()
        
        with tab2:
            show_experimental()
        
        with tab3:
            show_raw_data()
        
        with tab4:
            show_buckets()
            
        with tab5:
            show_insights()
            
    except Exception as e:
        st.error(f"Application error: {str(e)}")
        import traceback
        st.error(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main() 