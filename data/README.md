# Data Directory Structure

This directory contains all data files for the JerryVision application, organized in a hierarchical structure for clarity and maintainability.

## Directory Structure

```
data/
├── raw/                          # Original data from database
│   ├── raw_data.parquet         # 10-second OHLCV data from SPX
│   └── executed_trades.parquet  # Trade execution data
├── processed/                    # Derived/processed data
│   ├── resampled_30s_data.parquet  # 30-second resampled data
│   └── bucket_resampled/           # Bucket-specific resampled data
│       ├── Default.parquet         # Default bucket configuration
│       └── Custom_1.parquet        # Custom bucket configuration
├── metadata/                     # Configuration and status files
│   ├── bucket_version.parquet     # Bucket version definitions
│   ├── bucket_spec.parquet        # Bucket specifications
│   └── cache_status.json          # System cache status
└── cache/                        # Temporary cache files (if needed)
    └── .gitkeep                   # Placeholder to track directory
```

## File Descriptions

### Raw Data (`raw/`)
- **raw_data.parquet**: 10-second OHLCV (Open, High, Low, Close, Volume) data from SPX futures
- **executed_trades.parquet**: Historical trade execution data with P&L information

### Processed Data (`processed/`)
- **resampled_30s_data.parquet**: 30-second resampled data derived from raw 10-second data
- **bucket_resampled/**: Bucket-specific resampled data for different trading strategies
  - **Default.parquet**: Default bucket configuration data
  - **Custom_1.parquet**: Custom bucket configuration data

### Metadata (`metadata/`)
- **bucket_version.parquet**: Bucket version definitions and configurations
- **bucket_spec.parquet**: Detailed bucket specifications and parameters
- **cache_status.json**: System status including data freshness and file sizes

### Cache (`cache/`)
- Temporary cache files (currently empty)
- Used for intermediate processing results if needed

## Data Flow

1. **Raw Data**: Retrieved from SQL Server database (IBDB_DEV)
2. **Processing**: Raw data is resampled to 30-second intervals
3. **Bucket Processing**: Data is further processed into bucket-specific formats
4. **Caching**: All processed data is cached for fast access by the application

## File Sizes (Approximate)

- **raw_data.parquet**: ~5.8MB (10-second data)
- **resampled_30s_data.parquet**: ~2.4MB (30-second data)
- **executed_trades.parquet**: ~24KB (trade data)
- **bucket_resampled/**: ~128KB total (bucket-specific data)

## Maintenance

- Data is automatically refreshed via the application's refresh functionality
- Cache status is tracked in `metadata/cache_status.json`
- Old cache files are automatically cleaned up during refresh operations

## Notes

- All data files are in Parquet format for efficient storage and fast access
- The structure supports multiple bucket configurations for different trading strategies
- Metadata files contain configuration information that controls data processing 