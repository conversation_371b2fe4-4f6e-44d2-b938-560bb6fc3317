#!/usr/bin/env python3
"""
Example script demonstrating date-based filtering for Parquet data access in JerryVision.

This script shows how to use the new date filtering capabilities to improve
performance when working with time-series data.
"""

import pandas as pd
from datetime import datetime, date, timedelta
from core.data_processor import DataProcessor

def main():
    """Demonstrate date-based filtering functionality"""
    
    # Initialize data processor
    processor = DataProcessor()
    
    print("=== JerryVision Date-Based Filtering Demo ===\n")
    
    # Example 1: Load all data (original behavior)
    print("1. Loading all raw data (no filters):")
    start_time = datetime.now()
    all_data = processor.load_raw_data_from_cache()
    end_time = datetime.now()
    
    if not all_data.empty:
        print(f"   - Loaded {len(all_data)} rows")
        print(f"   - Date range: {all_data.index.min()} to {all_data.index.max()}")
        print(f"   - Load time: {(end_time - start_time).total_seconds():.3f} seconds")
        print(f"   - Memory usage: {all_data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    else:
        print("   - No data available")
    
    print()
    
    # Example 2: Load data for a specific date
    print("2. Loading data for a specific date:")
    target_date = "2024-01-15"  # Adjust this to a date that exists in your data
    
    start_time = datetime.now()
    filtered_data = processor.load_raw_data_from_cache(
        start_date=target_date,
        end_date=target_date
    )
    end_time = datetime.now()
    
    if not filtered_data.empty:
        print(f"   - Loaded {len(filtered_data)} rows for {target_date}")
        print(f"   - Date range: {filtered_data.index.min()} to {filtered_data.index.max()}")
        print(f"   - Load time: {(end_time - start_time).total_seconds():.3f} seconds")
        print(f"   - Memory usage: {filtered_data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    else:
        print(f"   - No data available for {target_date}")
    
    print()
    
    # Example 3: Load data for a date range
    print("3. Loading data for a date range:")
    start_date = "2024-01-10"
    end_date = "2024-01-15"
    
    start_time = datetime.now()
    range_data = processor.load_raw_data_from_cache(
        start_date=start_date,
        end_date=end_date
    )
    end_time = datetime.now()
    
    if not range_data.empty:
        print(f"   - Loaded {len(range_data)} rows from {start_date} to {end_date}")
        print(f"   - Date range: {range_data.index.min()} to {range_data.index.max()}")
        print(f"   - Load time: {(end_time - start_time).total_seconds():.3f} seconds")
        print(f"   - Memory usage: {range_data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    else:
        print(f"   - No data available for range {start_date} to {end_date}")
    
    print()
    
    # Example 4: Load specific columns only
    print("4. Loading specific columns only:")
    columns = ['close', 'volume']  # Only load close and volume columns
    
    start_time = datetime.now()
    column_data = processor.load_raw_data_from_cache(
        start_date=start_date,
        end_date=end_date,
        columns=columns
    )
    end_time = datetime.now()
    
    if not column_data.empty:
        print(f"   - Loaded {len(column_data)} rows with columns: {list(column_data.columns)}")
        print(f"   - Date range: {column_data.index.min()} to {column_data.index.max()}")
        print(f"   - Load time: {(end_time - start_time).total_seconds():.3f} seconds")
        print(f"   - Memory usage: {column_data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    else:
        print(f"   - No data available")
    
    print()
    
    # Example 5: Load resampled data with filters
    print("5. Loading resampled data with date filter:")
    
    start_time = datetime.now()
    resampled_data = processor.load_resampled_data_from_cache(
        start_date=start_date,
        end_date=end_date
    )
    end_time = datetime.now()
    
    if not resampled_data.empty:
        print(f"   - Loaded {len(resampled_data)} rows of resampled data")
        print(f"   - Date range: {resampled_data.index.min()} to {resampled_data.index.max()}")
        print(f"   - Load time: {(end_time - start_time).total_seconds():.3f} seconds")
        print(f"   - Memory usage: {resampled_data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    else:
        print(f"   - No resampled data available")
    
    print()
    
    # Example 6: Load bucket data with filters
    print("6. Loading bucket data with date filter:")
    
    start_time = datetime.now()
    bucket_data = processor.load_bucket_resampled_from_cache(
        bucket_name="Default",
        start_date=start_date,
        end_date=end_date
    )
    end_time = datetime.now()
    
    if bucket_data and "Default" in bucket_data:
        df = bucket_data["Default"]
        print(f"   - Loaded {len(df)} rows of Default bucket data")
        print(f"   - Date range: {df.index.min()} to {df.index.max()}")
        print(f"   - Load time: {(end_time - start_time).total_seconds():.3f} seconds")
        print(f"   - Memory usage: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    else:
        print(f"   - No Default bucket data available")
    
    print()
    
    # Performance comparison
    if not all_data.empty and not filtered_data.empty:
        print("=== Performance Comparison ===")
        all_data_size = all_data.memory_usage(deep=True).sum() / 1024 / 1024
        filtered_data_size = filtered_data.memory_usage(deep=True).sum() / 1024 / 1024
        
        print(f"All data: {len(all_data)} rows, {all_data_size:.2f} MB")
        print(f"Filtered data: {len(filtered_data)} rows, {filtered_data_size:.2f} MB")
        print(f"Memory reduction: {((all_data_size - filtered_data_size) / all_data_size * 100):.1f}%")
        print(f"Row reduction: {((len(all_data) - len(filtered_data)) / len(all_data) * 100):.1f}%")
    
    print("\n=== API Usage Examples ===")
    print("You can now use these parameters in API calls:")
    print("- /data/raw?start_date=2024-01-15&end_date=2024-01-15")
    print("- /data/resampled?start_date=2024-01-10&end_date=2024-01-15&columns=close,volume")
    print("- /data/bucket-resampled/Default?start_date=2024-01-15&limit=100")
    print("- /data/ndx?start_date=2024-01-15&end_date=2024-01-15&columns=close")

if __name__ == "__main__":
    main()
