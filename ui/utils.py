import streamlit as st
import pandas as pd
import humanize
from datetime import datetime
from .data_cache import get_cached_bucket_versions, get_cached_executed_trades, get_cached_trade_reports, get_cached_available_dates, get_cached_vix_raw_data
from .api_client import get_bucket_resampled_data

def fix_dataframe_types(df):
    """Fix column types to prevent Arrow serialization errors"""
    if df is None or df.empty:
        return df
    
    # Convert common problematic columns to string type
    string_columns = ['symbol', 'right', 'expiry', 'notes', 'account_id', 'account_name', 'order_type', 'status', 'side', 'ib_execution_id']
    for col in string_columns:
        if col in df.columns:
            df[col] = df[col].astype(str)
    
    # Convert numeric columns that might be mixed
    numeric_columns = ['price', 'volume', 'pnl', 'commission', 'strike', 'quantity']
    for col in numeric_columns:
        if col in df.columns:
            # Try to convert to numeric, fill NaN with 0 for numeric columns
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
    
    return df

def safe_dataframe_display(df, max_rows=1000):
    """Safely display a dataframe with row limit"""
    if len(df) > max_rows:
        st.warning(f"Showing first {max_rows} rows out of {len(df)} total rows")
        df = df.head(max_rows)
    
    st.dataframe(df, use_container_width=True, hide_index=True)

def format_trades_dataframe(df):
    """Format executed trades dataframe with proper formatting"""
    if df.empty:
        return df
    
    display_df = df.copy()
    
    # Format numeric columns
    numeric_columns = ['open', 'high', 'low', 'close', 'volume']
    for col in numeric_columns:
        if col in display_df.columns:
            if col == 'volume':
                display_df[col] = display_df[col].apply(lambda x: humanize.intword(x) if pd.notna(x) else "")
            else:
                display_df[col] = display_df[col].apply(lambda x: f"${humanize.intword(x)}" if pd.notna(x) else "")
    
    # Format specific columns
    if 'pnl' in display_df.columns:
        try:
            display_df['pnl'] = pd.to_numeric(display_df['pnl'], errors='coerce')
            display_df['pnl'] = display_df['pnl'].apply(lambda x: f"${humanize.intword(x)}" if pd.notna(x) else "N/A")
        except Exception as e:
            display_df['pnl'] = display_df['pnl'].astype(str)
    if 'commission' in display_df.columns:
        try:
            display_df['commission'] = pd.to_numeric(display_df['commission'], errors='coerce')
            display_df['commission'] = display_df['commission'].apply(lambda x: f"${humanize.intword(x)}" if pd.notna(x) else "N/A")
        except Exception as e:
            display_df['commission'] = display_df['commission'].astype(str)
    if 'entry_price' in display_df.columns:
        try:
            display_df['entry_price'] = pd.to_numeric(display_df['entry_price'], errors='coerce')
            display_df['entry_price'] = display_df['entry_price'].apply(lambda x: f"${humanize.intword(x)}" if pd.notna(x) else "N/A")
        except Exception as e:
            display_df['entry_price'] = display_df['entry_price'].astype(str)
    if 'exit_price' in display_df.columns:
        try:
            display_df['exit_price'] = pd.to_numeric(display_df['exit_price'], errors='coerce')
            display_df['exit_price'] = display_df['exit_price'].apply(lambda x: f"${humanize.intword(x)}" if pd.notna(x) else "N/A")
        except Exception as e:
            display_df['exit_price'] = display_df['exit_price'].astype(str)
    
    return display_df 

def get_shared_available_dates(selected_bucket_version=None):
    """Get available dates for the selected bucket version - shared across pages"""
    # Fallback to using bucket resampled data directly (original working approach)
    if selected_bucket_version is None:
        # Get default bucket version
        bucket_versions = get_cached_bucket_versions()
        if bucket_versions and bucket_versions.get('data'):
            selected_bucket_version = bucket_versions['data'][0]  # Use first available version
        else:
            return []

    bucket_name = selected_bucket_version.get('version', 'unknown')
    bucket_data = get_bucket_resampled_data(bucket_name, limit=50000)  # Reduced from 10M
    if not bucket_data:
        return []

    df = pd.DataFrame(bucket_data["data"])
    if df.empty:
        return []

    # Ensure timestamp column exists and convert to datetime
    if 'timestamp' not in df.columns:
        return []

    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)

    # Get available dates
    try:
        available_dates = sorted(pd.Series(df.index.date).unique())
        return available_dates
    except AttributeError:
        return []

def get_shared_date_state():
    """Get the shared date state across all pages"""
    # Initialize shared date state if not exists
    if 'shared_current_date_index' not in st.session_state:
        # Get available dates to set initial index
        available_dates = get_shared_available_dates()
        st.session_state.shared_current_date_index = len(available_dates) - 1 if available_dates else 0
    
    # Get available dates
    available_dates = get_shared_available_dates()
    
    # Ensure the current index is within bounds
    if available_dates:
        st.session_state.shared_current_date_index = max(0, min(st.session_state.shared_current_date_index, len(available_dates) - 1))
        selected_date = available_dates[st.session_state.shared_current_date_index]
    else:
        selected_date = datetime.now().date()
        st.session_state.shared_current_date_index = 0
    
    return selected_date, available_dates

def update_shared_date_state(new_date):
    """Update the shared date state"""
    available_dates = get_shared_available_dates()
    if new_date in available_dates:
        st.session_state.shared_current_date_index = available_dates.index(new_date)
        return True
    return False

def create_shared_date_controls(selected_date, available_dates, key_prefix="shared"):
    """Create shared date controls that work across all pages"""
    col1, col2 = st.columns([1, 2])
    
    with col1:
        # Date entry on top
        manual_date = st.date_input(
            "Enter Date",
            value=selected_date,
            key=f"{key_prefix}_manual_date"
        )
        
        # Handle manual date input - only allow dates that have data
        if manual_date != selected_date:
            if manual_date in available_dates:
                # Valid date with data - update the shared selection
                update_shared_date_state(manual_date)
                st.rerun()
            else:
                # Invalid date - show warning and keep current selection
                st.warning(f"⚠️ No data available for {manual_date}.")

    with col2:
        # Navigation buttons below
        nav_col1, nav_col2, nav_col3, nav_col4, nav_col5 = st.columns(5)
        
        with nav_col1:
            if st.button("⏮️", disabled=not available_dates or st.session_state.shared_current_date_index == 0, key=f"{key_prefix}_first_btn"):
                st.session_state.shared_current_date_index = 0
                st.rerun()
        
        with nav_col2:
            if st.button("⬅️", disabled=not available_dates or st.session_state.shared_current_date_index == 0, key=f"{key_prefix}_prev_btn"):
                st.session_state.shared_current_date_index -= 1
                st.rerun()
        
        with nav_col3:
            if st.button("🎲", disabled=not available_dates, key=f"{key_prefix}_random_btn"):
                import random
                random_index = random.randint(0, len(available_dates) - 1)
                st.session_state.shared_current_date_index = random_index
                st.rerun()
        
        with nav_col4:
            if st.button("➡️", disabled=not available_dates or st.session_state.shared_current_date_index == len(available_dates) - 1, key=f"{key_prefix}_next_btn"):
                st.session_state.shared_current_date_index += 1
                st.rerun()
        
        with nav_col5:
            if st.button("⏭️", disabled=not available_dates or st.session_state.shared_current_date_index == len(available_dates) - 1, key=f"{key_prefix}_last_btn"):
                st.session_state.shared_current_date_index = len(available_dates) - 1
                st.rerun()

def display_executed_trades(selected_date):
    """Display executed trades for the selected date"""
    # Get executed trades data for the selected date
    date_str = selected_date.strftime('%Y-%m-%d')
    executed_trades_data = get_cached_executed_trades(date=date_str)

    if executed_trades_data and executed_trades_data.get('data'):
        trades_list = executed_trades_data['data']

        if trades_list:
            st.subheader("💰 Executed Trades")

            # Use columns to display trades side by side
            if len(trades_list) <= 3:
                cols = st.columns(len(trades_list))
            else:
                # If more than 3 trades, use 3 columns and wrap
                cols = st.columns(3)

            for i, trade in enumerate(trades_list):
                col_index = i % len(cols)

                with cols[col_index]:
                    # Extract trade information
                    trade_id = trade.get('id', f'Trade {i+1}')
                    symbol = trade.get('symbol', 'N/A')

                    # Build side information from available fields
                    right = trade.get('right', '')  # C for Call, P for Put
                    strike = trade.get('strike', '')
                    expiry = trade.get('expiry', '')

                    # Create a more descriptive side field (without date)
                    if right and strike:
                        side = f"{int(strike)} {right}"
                    else:
                        side = trade.get('side', 'N/A')

                    quantity = trade.get('quantity', 'N/A')
                    price = trade.get('entry_price', trade.get('price', 'N/A'))  # Use entry_price primarily
                    status = trade.get('status', 'N/A')
                    pnl = trade.get('pnl', trade.get('profit_loss', 'N/A'))
                    # Use entry_time as the primary execution time
                    execution_time = trade.get('entry_time', trade.get('execution_time', trade.get('timestamp', 'N/A')))

                    # Format price and PnL
                    if price != 'N/A' and price is not None:
                        try:
                            price = f"${float(price):.2f}"
                        except (ValueError, TypeError):
                            pass

                    if pnl != 'N/A' and pnl is not None and str(pnl).lower() != 'nan':
                        try:
                            pnl_value = float(pnl)
                            if not pd.isna(pnl_value):  # Check for NaN
                                pnl_color = "🟢" if pnl_value >= 0 else "🔴"
                                pnl = f"{pnl_color} ${pnl_value:.2f}"
                            else:
                                pnl = 'N/A'
                        except (ValueError, TypeError):
                            pnl = 'N/A'

                    # Format execution time
                    if execution_time != 'N/A' and execution_time is not None:
                        try:
                            if isinstance(execution_time, str):
                                # Try to parse and format the time
                                from datetime import datetime
                                dt = datetime.fromisoformat(execution_time.replace('Z', '+00:00'))
                                execution_time = dt.strftime('%H:%M:%S')
                        except:
                            pass  # Keep original format if parsing fails

                    # Create expandable trade details
                    account_name = trade.get('account_name', 'N/A')
                    trade_title = f"{symbol} {side} {quantity}"
                    if price != 'N/A':
                        trade_title += f" @ {price}"

                    # Add account name and status to title
                    if account_name != 'N/A':
                        trade_title += f" | {account_name}"
                    if status != 'N/A':
                        trade_title += f" | {status}"

                    with st.expander(trade_title, expanded=False):
                        st.write(f"**Trade ID:** {trade_id}")
                        st.write(f"**Status:** {status}")
                        if pnl != 'N/A':
                            st.write(f"**P&L:** {pnl}")
                        if execution_time != 'N/A':
                            st.write(f"**Entry Time:** {execution_time}")

                        # Show exit time if available and different from entry time
                        exit_time = trade.get('exit_time')
                        if exit_time and exit_time != 'N/A' and str(exit_time) != 'NaT':
                            try:
                                if isinstance(exit_time, str):
                                    from datetime import datetime
                                    dt = datetime.fromisoformat(exit_time.replace('Z', '+00:00'))
                                    exit_time = dt.strftime('%H:%M:%S')
                            except:
                                pass  # Keep original format if parsing fails
                            st.write(f"**Exit Time:** {exit_time}")

                        # Show any additional fields that might be in the trade data
                        additional_fields = ['commission', 'fees', 'notes', 'entry_price', 'exit_price', 'strike', 'right']
                        for field in additional_fields:
                            if field in trade and trade[field] is not None and str(trade[field]).lower() not in ['nan', 'none', '']:
                                field_name = field.replace('_', ' ').title()
                                value = trade[field]

                                # Format specific fields
                                if field in ['entry_price', 'exit_price'] and value != 'N/A':
                                    try:
                                        value = f"${float(value):.2f}"
                                    except (ValueError, TypeError):
                                        pass

                                elif field == 'commission' and value != 'N/A':
                                    try:
                                        value = f"${float(value):.2f}"
                                    except (ValueError, TypeError):
                                        pass

                                st.write(f"**{field_name}:** {value}")
        else:
            st.info(f"No executed trades found for {date_str}")
    else:
        st.info("No executed trades data available for this date")

def display_trade_reports(selected_date):
    """Display trade reports for the selected date"""
    # Get VIX close for the selected date (always show VIX)
    vix_close = get_vix_close_for_date(selected_date)
    vix_display = f"VIX: {vix_close:.2f}" if vix_close is not None else "VIX: N/A"

    # Get trade reports data for the selected date
    date_str = selected_date.strftime('%Y-%m-%d')
    trade_reports_data = get_cached_trade_reports(date=date_str)

    if trade_reports_data and trade_reports_data.get('data'):
        reports_list = trade_reports_data['data']

        if reports_list:
            # Display heading with VIX close right-justified using columns
            col1, col2 = st.columns([3, 1])
            with col1:
                st.subheader("📊 Trade Reports")
            with col2:
                st.markdown(f"<div style='text-align: right; margin-top: 0.5rem;'><strong>{vix_display}</strong></div>", unsafe_allow_html=True)

            # Stack trade reports vertically (no columns)
            for i, report in enumerate(reports_list):
                # Extract report information
                report_id = report.get('id', f'Report {i+1}')

                # Extract key information for the expander title
                account_name = report.get('account_name') or report.get('account') or 'N/A'

                # Get timestamp and format it in EST
                timestamp = report.get('session_start_time') or report.get('created_at') or report.get('timestamp') or report.get('date')
                time_str = 'N/A'
                if timestamp is not None:
                    try:
                        from datetime import datetime
                        import pytz

                        # Handle both string and datetime objects
                        if isinstance(timestamp, str):
                            # Parse the timestamp (assume UTC if no timezone info)
                            if timestamp.endswith('Z'):
                                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                            elif '+' in timestamp or timestamp.endswith('UTC'):
                                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                            else:
                                # Assume UTC if no timezone info
                                dt = datetime.fromisoformat(timestamp)
                                dt = dt.replace(tzinfo=pytz.UTC)
                        else:
                            # Handle datetime objects (assume UTC if no timezone)
                            dt = timestamp
                            if dt.tzinfo is None:
                                dt = dt.replace(tzinfo=pytz.UTC)

                        # Convert to EST/EDT
                        est = pytz.timezone('US/Eastern')
                        dt_est = dt.astimezone(est)
                        time_str = dt_est.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        time_str = str(timestamp) if timestamp else 'N/A'

                # Get trading mode
                trading_mode = report.get('trading_mode') or 'N/A'

                # Get final decision
                final_decision = report.get('final_decision') or 'N/A'

                # Get execution status and format it for display
                raw_execution_status = report.get('execution_status') or report.get('status') or 'N/A'

                # Format execution status for display in title
                if raw_execution_status and raw_execution_status != 'N/A':
                    status_lower = str(raw_execution_status).lower()
                    if 'fill' in status_lower or 'success' in status_lower or 'complete' in status_lower:
                        execution_status = 'Filled'
                    elif 'fail' in status_lower or 'error' in status_lower or 'reject' in status_lower:
                        execution_status = 'Failed'
                    else:
                        execution_status = str(raw_execution_status)
                else:
                    execution_status = 'N/A'

                # Get primary strike
                primary_strike = report.get('primary_strike')
                if primary_strike is not None:
                    try:
                        primary_strike = f"{float(primary_strike):.1f}"
                    except (ValueError, TypeError):
                        primary_strike = str(primary_strike) if primary_strike else 'N/A'
                else:
                    primary_strike = 'N/A'

                # Get execution price
                execution_price = report.get('execution_price')
                if execution_price is not None:
                    try:
                        # Handle NaN values
                        if str(execution_price).lower() in ['nan', 'none', '']:
                            execution_price = 'N/A'
                        else:
                            execution_price = f"${float(execution_price):.2f}"
                    except (ValueError, TypeError):
                        execution_price = str(execution_price) if execution_price else 'N/A'
                else:
                    execution_price = 'N/A'

                # Build report title with only values separated by |
                title_parts = []
                if account_name and account_name != 'N/A':
                    title_parts.append(str(account_name))
                if time_str and time_str != 'N/A':
                    title_parts.append(str(time_str))
                if trading_mode and trading_mode != 'N/A':
                    title_parts.append(str(trading_mode))
                if final_decision and final_decision != 'N/A':
                    title_parts.append(str(final_decision))
                if execution_status and execution_status != 'N/A':
                    title_parts.append(str(execution_status))
                if primary_strike and primary_strike != 'N/A':
                    title_parts.append(str(primary_strike))
                if execution_price and execution_price != 'N/A':
                    title_parts.append(str(execution_price))

                report_title = " | ".join(title_parts) if title_parts else f"Trade Report {i+1}"

                # Display each report as a full-width expander
                with st.expander(report_title, expanded=False):
                    # Show report content first without label if it exists
                    report_content = report.get('report_content')
                    if report_content is not None and str(report_content).lower() not in ['nan', 'none', '']:
                        # Remove the header line that starts with "# SPX Trading Report" or similar
                        content_lines = str(report_content).split('\n')
                        filtered_lines = []
                        for line in content_lines:
                            # Skip lines that contain trading report headers
                            if not (line.strip().startswith('#') and 'Trading Report' in line):
                                filtered_lines.append(line)

                        # Join the remaining lines and display if there's content left
                        filtered_content = '\n'.join(filtered_lines).strip()
                        if filtered_content:
                            st.markdown(filtered_content)

                    # Show all other fields in the report
                    excluded_fields = ['id', 'report_type', 'type', 'symbol', 'ticker', 'status', 'execution_status', 'created_at', 'timestamp', 'date', 'session_start_time', 'account_name', 'account', 'trading_mode', 'final_decision', 'primary_strike', 'execution_price', 'index', 'account_id', 'session_date', 'session_end_time', 'session_duration_seconds', 'trade_executed', 'retry_attempts', 'bar_count', 'span3_value', 'close_percentage', 'total_events', 'report_content']
                    for field, value in report.items():
                        if field not in excluded_fields and value is not None and str(value).lower() not in ['nan', 'none', '']:
                            field_name = field.replace('_', ' ').title()

                            # Format monetary values
                            if any(money_field in field.lower() for money_field in ['price', 'cost', 'value', 'pnl', 'profit', 'loss']):
                                try:
                                    value = f"${float(value):.2f}"
                                except (ValueError, TypeError):
                                    pass

                            st.write(f"**{field_name}:** {value}")
        else:
            # Display heading with VIX even when no trade reports
            col1, col2 = st.columns([3, 1])
            with col1:
                st.subheader("📊 Trade Reports")
            with col2:
                st.markdown(f"<div style='text-align: right; margin-top: 0.5rem;'><strong>{vix_display}</strong></div>", unsafe_allow_html=True)
            st.info(f"No trade reports found for {date_str}")
    else:
        # Display heading with VIX even when no trade reports data available
        col1, col2 = st.columns([3, 1])
        with col1:
            st.subheader("📊 Trade Reports")
        with col2:
            st.markdown(f"<div style='text-align: right; margin-top: 0.5rem;'><strong>{vix_display}</strong></div>", unsafe_allow_html=True)
        st.info("No trade reports data available for this date")


def get_vix_close_for_date(selected_date):
    """Get VIX close price for a specific date"""
    try:
        # Get VIX data
        vix_data = get_cached_vix_raw_data(limit=10000)
        if not vix_data or not vix_data.get('data'):
            return None

        # Convert to DataFrame
        vix_df = pd.DataFrame(vix_data['data'])
        if vix_df.empty:
            return None

        # Ensure date column exists and convert to datetime
        if 'date' in vix_df.columns:
            vix_df['date'] = pd.to_datetime(vix_df['date'])
            vix_df.set_index('date', inplace=True)
        else:
            return None

        # Filter for the selected date
        selected_date_str = selected_date.strftime('%Y-%m-%d')
        date_filtered = vix_df[vix_df.index.date == pd.to_datetime(selected_date_str).date()]

        if date_filtered.empty:
            return None

        # Get the last close price for that date (end of day)
        if 'close' in date_filtered.columns:
            last_close = date_filtered['close'].iloc[-1]
            return float(last_close)

        return None
    except Exception as e:
        # Silently handle errors to avoid breaking the page
        return None
