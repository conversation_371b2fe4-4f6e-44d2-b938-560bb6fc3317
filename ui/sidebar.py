import streamlit as st
from .data_cache import get_cached_cache_status
from .api_client import refresh_data

def show_data_overview_sidebar():
    """Show data overview in sidebar"""
    st.sidebar.header("📊 Data Overview")
    
    # Get cache status
    status = get_cached_cache_status()
    if status:
        
        st.sidebar.metric("Raw Data", f"{status.get('raw_data_size', 0):,}" if status.get('raw_data_exists') else "None")
        st.sidebar.metric("Resampled", f"{status.get('resampled_data_size', 0):,}" if status.get('resampled_data_exists') else "None")

        st.sidebar.metric("Bucket Versions", f"{status.get('bucket_version_size', 0):,}" if status.get('bucket_version_exists') else "None")
        
        if status.get('last_refresh'):
            st.sidebar.caption(f"Last refresh: {status['last_refresh']}")
    
    # Refresh button
    if st.sidebar.button("🔄 Refresh Data", type="primary"):
        refresh_data() 