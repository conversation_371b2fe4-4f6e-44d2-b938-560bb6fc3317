import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime
from ..data_cache import get_cached_bucket_versions, get_cached_bucket_resampled_data, get_cached_ndx_resampled_data, get_cached_backtest_latest_saved
from ..api_client import get_bucket_resampled_data, call_api
from ..utils import safe_dataframe_display, get_shared_date_state, create_shared_date_controls, display_executed_trades, display_trade_reports, get_vix_close_for_date
from core.config import settings

def create_spx_chart(df_filtered, selected_date, backtest_data=None):
    """Create the SPX chart with candlestick and volume"""
    if len(df_filtered) == 0:
        return None
    
    # Create time labels for x-axis
    time_labels = [idx.strftime('%H:%M:%S') for idx in df_filtered.index]
    
    # Get the actual time range of the data
    data_start_time = df_filtered.index.min()
    data_end_time = df_filtered.index.max()
    
    # Add padding to prevent cutoff
    padding = pd.Timedelta(seconds=10)
    chart_start_time = data_start_time - padding
    chart_end_time = data_end_time + padding
    
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=(f'{selected_date} (30s Resampled)', 'Volume'),
        row_width=[0.3, 0.7]
    )
    
    # Candlestick chart
    # Calculate span and %close for tooltips
    span = df_filtered['high'] - df_filtered['low']
    pct_close = ((df_filtered['close'] - df_filtered['low']) / span * 100).fillna(0)

    # Calculate accumulated span since 15:50:00 and percentage close based on accumulated span
    start_time_1550 = pd.to_datetime('15:50:00').time()

    # Create custom hover text
    hover_text = []
    for i, (idx, s, p) in enumerate(zip(df_filtered.index, span, pct_close)):
        # Get OHLC values for this row
        row = df_filtered.iloc[i]
        open_val = row['open']
        high_val = row['high']
        low_val = row['low']
        close_val = row['close']

        # Calculate accumulated span since 15:50:00
        data_since_1550 = df_filtered[df_filtered.index.time >= start_time_1550]
        data_up_to_current = data_since_1550[data_since_1550.index <= idx]

        if not data_up_to_current.empty:
            # Get the accumulated high and low since 15:50:00
            accumulated_high = data_up_to_current['high'].max()
            accumulated_low = data_up_to_current['low'].min()
            accumulated_span = accumulated_high - accumulated_low

            # Get the last close in the accumulated span (which is the current close)
            last_close = data_up_to_current['close'].iloc[-1]

            # Calculate percentage close based on accumulated span
            if accumulated_span > 0:
                accumulated_pct_close = ((last_close - accumulated_low) / accumulated_span * 100)
            else:
                accumulated_pct_close = 0
        else:
            accumulated_span = 0
            accumulated_pct_close = 0

        hover_text.append(
            f"Time: {idx.strftime('%H:%M:%S')}<br>" +
            f"Open: ${open_val:.2f}<br>" +
            f"High: ${high_val:.2f}<br>" +
            f"Low: ${low_val:.2f}<br>" +
            f"Close: ${close_val:.2f}<br>" +
            f"Span: ${s:.2f}<br>" +
            f"%Close: {p:.1f}%<br>" +
            f"<br>" +
            f"Accum Span (15:50): ${accumulated_span:.2f}<br>" +
            f"Accum %Close: {accumulated_pct_close:.1f}%"
        )
    
    fig.add_trace(
        go.Candlestick(
            x=time_labels,
            open=df_filtered['open'],
            high=df_filtered['high'],
            low=df_filtered['low'],
            close=df_filtered['close'],
            name="OHLC",
            increasing_line_color='#26A69A',
            decreasing_line_color='#EF5350',
            hovertext=hover_text,
            hoverinfo='text'
        ),
        row=1, col=1
    )
    
    # Volume chart
    colors = ['#26A69A' if close >= open else '#EF5350' 
              for close, open in zip(df_filtered['close'], df_filtered['open'])]
    
    fig.add_trace(
        go.Bar(
            x=time_labels,
            y=df_filtered['volume'],
            name="Volume",
            marker_color=colors
        ),
        row=2, col=1
    )
    
    # Update layout
    fig.update_layout(
        title="",
        xaxis_rangeslider_visible=False,
        height=600,
        showlegend=False,
        template="plotly_white"
    )
    
    # Update Y-axis for volume
    fig.update_yaxes(title_text="Price ($)", row=1, col=1)
    fig.update_yaxes(title_text="Volume", row=2, col=1)
    
    return fig

def display_backtest_trades(backtest_data, selected_date):
    """Display backtest trades in a reusable function"""
    if backtest_data is not None and not backtest_data.empty:
        # Convert trading_date to datetime for comparison
        backtest_data['trading_date'] = pd.to_datetime(backtest_data['trading_date'])
        
        # Filter backtest data for the selected date
        date_backtest = backtest_data[backtest_data['trading_date'].dt.date == selected_date]
        
        if not date_backtest.empty:
            # Use columns to make it more compact
            cols = st.columns(len(date_backtest))
            for i, (idx, trade) in enumerate(date_backtest.iterrows()):
                decision = trade.get('decision', 'Unknown')
                target_price = trade.get('target_price', 'N/A')
                # Format target_price as integer if it's a number
                if target_price != 'N/A' and target_price is not None:
                    try:
                        target_price = int(float(target_price))
                    except (ValueError, TypeError):
                        pass  # Keep as is if conversion fails
                spread_price = trade.get('spread_price', 'N/A')
                final_strikes = trade.get('final_strikes', 'N/A')
                
                with cols[i]:
                    with st.expander(f"Trade {i+1}: {decision} - {target_price}", expanded=False):
                        st.write(f"**Spread Price:** ${spread_price}")
                        st.write(f"**Final Strikes:** {final_strikes}")

def display_backtest_latest_saved_records(selected_date):
    """Display backtest_latest_saved records for the selected date as expanders"""
    backtest_data = get_cached_backtest_latest_saved()
    if not backtest_data or not backtest_data.get('data'):
        return
    import pandas as pd
    df = pd.DataFrame(backtest_data['data'])
    if df.empty or 'trading_date' not in df.columns:
        return
    df['trading_date'] = pd.to_datetime(df['trading_date'])
    date_records = df[df['trading_date'].dt.date == selected_date]
    if date_records.empty:
        return
    st.subheader("🧪 Backtest Records")
    fields_to_exclude = [
        'processing_time_seconds',
        'created_at',
        'narrow_fill_ohlc',
        'narrow_fill_consolidated_ohlc'
    ]
    for i, (_, record) in enumerate(date_records.iterrows()):
        title_parts = []
        # Compose a concise title from key fields
        for field in ['decision', 'target_price', 'spread_price', 'final_strikes']:
            val = record.get(field)
            if pd.notna(val) and val != 'N/A':
                title_parts.append(f"{field.replace('_',' ').title()}: {val}")
        expander_title = f"Backtest {i+1}: " + " | ".join(title_parts) if title_parts else f"Backtest {i+1}"
        with st.expander(expander_title, expanded=False):
            for col, val in record.items():
                if pd.notna(val) and col != 'trading_date' and col not in fields_to_exclude:
                    st.write(f"**{col.replace('_',' ').title()}:** {val}")

def show_spx_tab(selected_bucket_version, selected_date, backtest_data):
    """Show SPX data in a tab"""
    # Load data based on selected bucket version
    bucket_name = selected_bucket_version.get('version', 'unknown')
    bucket_data = get_cached_bucket_resampled_data(bucket_name, limit=10000000)
    if not bucket_data:
        st.error(f"Failed to load bucket resampled data for version: {bucket_name}")
        return

    df = pd.DataFrame(bucket_data["data"])
    if df.empty:
        st.warning(f"No bucket resampled data available for version: {bucket_name}")
        return
    
    # Handle timestamp column - check for both 'date' and 'timestamp' column names
    timestamp_col = None
    if 'timestamp' in df.columns:
        timestamp_col = 'timestamp'
    elif 'date' in df.columns:
        timestamp_col = 'date'
    else:
        st.error("No timestamp or date column found in resampled data")
        st.write("Available columns:", list(df.columns))
        return
    
    df[timestamp_col] = pd.to_datetime(df[timestamp_col])
    df.set_index(timestamp_col, inplace=True)
    
    # Get available dates - handle potential index issues
    try:
        available_dates = sorted(pd.Series(df.index.date).unique())
    except AttributeError as e:
        df.index = pd.to_datetime(df.index)
        available_dates = sorted(pd.Series(df.index.date).unique())
    
    if not available_dates:
        st.warning("No SPX data available")
        return
    
    # Validate that selected_date is in available_dates
    if selected_date not in available_dates:
        st.warning(f"⚠️ No SPX data available for {selected_date}.")
        return
    
    # Filter data for selected date (show all data for the day)
    try:
        df_daily = df[df.index.date == selected_date]
    except AttributeError:
        df.index = pd.to_datetime(df.index)
        df_daily = df[df.index.date == selected_date]
    
    if df_daily.empty:
        st.warning(f"No SPX data available for {selected_date}")
        return
    
    # --- Apply end-of-day time filtering ---
    start_time = settings.DEFAULT_START_TIME
    end_time = settings.DEFAULT_END_TIME
    df_filtered = df_daily.between_time(start_time, end_time)
    # --------------------------------------
    
    if df_filtered.empty:
        st.warning(f"No data available for {selected_date} in the end-of-day window")
        return
    
    # Display backtest decisions and trading details above the chart
    display_backtest_trades(backtest_data, selected_date)

    # Display trade reports for the selected date
    display_trade_reports(selected_date)

    # Display backtest_latest_saved records for the selected date
    display_backtest_latest_saved_records(selected_date)

    result = create_spx_chart(df_filtered, selected_date, backtest_data)
    if result:
        st.plotly_chart(result, use_container_width=True, key="spx_resampled_data_chart")

def show_ndx_tab(selected_bucket_version, selected_date, backtest_data):
    """Show NDX data in a tab

    Note: NDX data is not affected by bucket versions - it uses standard resampled data
    """
    ndx_data = get_cached_ndx_resampled_data(limit=10000000)
    if not ndx_data:
        st.error("Failed to load NDX resampled data")
        return
    
    df = pd.DataFrame(ndx_data["data"])
    if df.empty:
        st.warning("No NDX resampled data available")
        return
    
    # Handle timestamp column - check for both 'date' and 'timestamp' column names
    timestamp_col = None
    if 'timestamp' in df.columns:
        timestamp_col = 'timestamp'
    elif 'date' in df.columns:
        timestamp_col = 'date'
    else:
        st.error("No timestamp or date column found in NDX data")
        st.write("Available columns:", list(df.columns))
        return
    
    df[timestamp_col] = pd.to_datetime(df[timestamp_col])
    df.set_index(timestamp_col, inplace=True)
    
    # Get available dates - handle potential index issues
    try:
        available_dates = sorted(pd.Series(df.index.date).unique())
    except AttributeError as e:
        df.index = pd.to_datetime(df.index)
        available_dates = sorted(pd.Series(df.index.date).unique())
    
    if not available_dates:
        st.warning("No NDX data available")
        return
    
    # Validate that selected_date is in available_dates
    if selected_date not in available_dates:
        st.warning(f"⚠️ No NDX data available for {selected_date}.")
        return
    
    # Filter data for selected date (show all data for the day)
    try:
        df_daily = df[df.index.date == selected_date]
    except AttributeError:
        df.index = pd.to_datetime(df.index)
        df_daily = df[df.index.date == selected_date]
    
    if df_daily.empty:
        st.warning(f"No NDX data available for {selected_date}")
        return
    
    # --- Apply end-of-day time filtering ---
    start_time = settings.DEFAULT_START_TIME
    end_time = settings.DEFAULT_END_TIME
    df_filtered = df_daily.between_time(start_time, end_time)
    # --------------------------------------
    
    if df_filtered.empty:
        st.warning(f"No NDX data available for {selected_date} in the end-of-day window")
        return
    
    # Display backtest decisions and trading details above the chart
    display_backtest_trades(backtest_data, selected_date)

    # Display trade reports for the selected date
    display_trade_reports(selected_date)

    # Display backtest_latest_saved records for the selected date
    display_backtest_latest_saved_records(selected_date)

    result = create_spx_chart(df_filtered, selected_date, backtest_data)
    if result:
        st.plotly_chart(result, use_container_width=True, key="ndx_resampled_data_chart")

def show_resampled_data():
    """Show resampled data page"""
    # Add custom CSS for compact styling
    st.markdown("""
        <style>
        .compact-header {
            font-size: 1.2em !important;
            margin-bottom: 0.5em !important;
        }
        .stDataFrame {
            margin-top: 0.5em !important;
        }
        .stSelectbox > div > div {
            padding: 0.25rem 0.5rem !important;
        }
        .stTabs [data-baseweb="tab-list"] {
            gap: 0.5rem !important;
        }
        .stTabs [data-baseweb="tab"] {
            padding: 0.5rem 1rem !important;
        }
        .stButton > button {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.8rem !important;
        }
        </style>
        """, unsafe_allow_html=True)
    
    # Get bucket versions for selection
    bucket_versions = get_cached_bucket_versions()
    available_bucket_versions = []
    if bucket_versions and bucket_versions.get('data'):
        available_bucket_versions = bucket_versions['data']
    
    bucket_version_options = available_bucket_versions
    
    # Get shared date state
    selected_date, shared_available_dates = get_shared_date_state()

    # Create a row with bucket version selector and navigation
    col1, col2 = st.columns([1, 2])
    
    with col1:
        # Bucket version selector
        selected_bucket_version = st.selectbox(
            "Select Bucket Version:",
            options=bucket_version_options,
            format_func=lambda x: x.get('version', 'Unknown'),
            key="resampled_bucket_version_selector"
        )
    
    with col2:
        # Date controls (shared across pages)
         create_shared_date_controls(selected_date, shared_available_dates, "resampled")
    
    # No backtest data needed
    backtest_data = None
    
    # Create tabs for different tickers
    tab1, tab2 = st.tabs(["📈 SPX", "📊 NDX"])
    
    with tab1:
        show_spx_tab(selected_bucket_version, selected_date, backtest_data)
    
    with tab2:
        show_ndx_tab(selected_bucket_version, selected_date, backtest_data)

