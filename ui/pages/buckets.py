import streamlit as st
from ..data_cache import get_cached_bucket_versions, get_cached_bucket_specs
from ..api_client import (
    create_bucket_version, update_bucket_version, delete_bucket_version,
    create_bucket_spec, update_bucket_spec, delete_bucket_spec
)

def show_buckets():
    """Show buckets page"""
    # Get bucket versions
    bucket_versions = get_cached_bucket_versions()
    if not bucket_versions or not bucket_versions.get('data'):
        st.error("Failed to load bucket versions")
        return
    
    # Create tabs for different bucket operations
    tab1, tab2 = st.tabs(["Bucket Versions", "Bucket Specs"])
    
    with tab1:
        show_bucket_versions_crud(bucket_versions['data'])
    
    with tab2:
        show_bucket_specs_crud(bucket_versions['data'])

def show_bucket_versions_crud(bucket_versions):
    """Show bucket versions CRUD interface"""
    st.subheader("Bucket Versions")
    
    # Display existing bucket versions
    if bucket_versions:
        for version in bucket_versions:
            st.write(f"• {version.get('version')}")
    else:
        st.info("No bucket versions found")
    
    # Create new bucket version
    st.subheader("Create New Bucket Version")
    with st.form("create_bucket_version"):
        new_version_name = st.text_input("Version Name", key="new_bucket_version_name")
        submitted = st.form_submit_button("Create Bucket Version")
        
        if submitted and new_version_name:
            result = create_bucket_version({"version": new_version_name})
            if result and result.get("success"):
                st.success(f"✅ Bucket version '{new_version_name}' created successfully!")
                st.rerun()
            else:
                st.error(f"❌ Failed to create bucket version: {result.get('message', 'Unknown error')}")
    
    # Update existing bucket version
    if bucket_versions:
        st.subheader("Update Bucket Version")
        with st.form("update_bucket_version"):
            version_options = {v['version']: v['id'] for v in bucket_versions}
            selected_version = st.selectbox("Select Version to Update", list(version_options.keys()), key="update_version_select")
            updated_version_name = st.text_input("New Version Name", key="updated_bucket_version_name")
            submitted = st.form_submit_button("Update Bucket Version")
            
            if submitted and updated_version_name:
                version_id = version_options[selected_version]
                result = update_bucket_version(version_id, {"version": updated_version_name})
                if result and result.get("success"):
                    st.success(f"✅ Bucket version updated successfully!")
                    st.rerun()
                else:
                    st.error(f"❌ Failed to update bucket version: {result.get('message', 'Unknown error')}")
    
    # Delete bucket version
    if bucket_versions:
        st.subheader("Delete Bucket Version")
        with st.form("delete_bucket_version"):
            version_options = {v['version']: v['id'] for v in bucket_versions}
            selected_version = st.selectbox("Select Version to Delete", list(version_options.keys()), key="delete_version_select")
            confirm_delete = st.checkbox("I understand this will delete the bucket version and all associated specs", key="confirm_delete_version")
            submitted = st.form_submit_button("Delete Bucket Version", type="secondary")
            
            if submitted and confirm_delete:
                version_id = version_options[selected_version]
                result = delete_bucket_version(version_id)
                if result and result.get("success"):
                    st.success(f"✅ Bucket version deleted successfully!")
                    st.rerun()
                else:
                    st.error(f"❌ Failed to delete bucket version: {result.get('message', 'Unknown error')}")

def show_bucket_specs_crud(bucket_versions):
    """Show bucket specs CRUD interface"""
    st.subheader("Bucket Specs")
    
    # Get bucket specs for the selected version
    if bucket_versions:
        version_options = {v['version']: v['id'] for v in bucket_versions}
        selected_version = st.selectbox("Select Bucket Version", list(version_options.keys()), key="bucket_specs_version_select")
        selected_version_id = version_options[selected_version]
        
        # Get bucket specs for this version
        bucket_specs = get_cached_bucket_specs(selected_version_id)
        
        if bucket_specs and bucket_specs.get('data'):
            st.write(f"**Bucket Specs for Version {selected_version}:**")
            for spec in bucket_specs['data']:
                st.write(f"• {spec.get('bucket_start')}")
        else:
            st.info(f"No bucket specs found for version {selected_version}")
        
        # Create new bucket spec
        st.subheader("Create New Bucket Spec")
        with st.form("create_bucket_spec"):
            bucket_start_time = st.text_input("Bucket Start Time (HH:MM:SS)", key="new_bucket_start_time")
            submitted = st.form_submit_button("Create Bucket Spec")
            
            if submitted and bucket_start_time:
                result = create_bucket_spec({
                    "bucket_start": bucket_start_time,
                    "bucket_version_id": selected_version_id
                })
                if result and result.get("success"):
                    st.success(f"✅ Bucket spec created successfully!")
                    st.rerun()
                else:
                    st.error(f"❌ Failed to create bucket spec: {result.get('message', 'Unknown error')}")
        
        # Update existing bucket spec
        if bucket_specs and bucket_specs.get('data'):
            st.subheader("Update Bucket Spec")
            with st.form("update_bucket_spec"):
                spec_options = {s['bucket_start']: s['id'] for s in bucket_specs['data']}
                selected_spec = st.selectbox("Select Spec to Update", list(spec_options.keys()), key="update_spec_select")
                updated_start_time = st.text_input("New Start Time (HH:MM:SS)", key="updated_bucket_start_time")
                submitted = st.form_submit_button("Update Bucket Spec")
                
                if submitted and updated_start_time:
                    spec_id = spec_options[selected_spec]
                    result = update_bucket_spec(spec_id, {"bucket_start": updated_start_time})
                    if result and result.get("success"):
                        st.success(f"✅ Bucket spec updated successfully!")
                        st.rerun()
                    else:
                        st.error(f"❌ Failed to update bucket spec: {result.get('message', 'Unknown error')}")
        
        # Delete bucket spec
        if bucket_specs and bucket_specs.get('data'):
            st.subheader("Delete Bucket Spec")
            with st.form("delete_bucket_spec"):
                spec_options = {s['bucket_start']: s['id'] for s in bucket_specs['data']}
                selected_spec = st.selectbox("Select Spec to Delete", list(spec_options.keys()), key="delete_spec_select")
                confirm_delete = st.checkbox("I understand this will delete the bucket spec", key="confirm_delete_spec")
                submitted = st.form_submit_button("Delete Bucket Spec", type="secondary")
                
                if submitted and confirm_delete:
                    spec_id = spec_options[selected_spec]
                    result = delete_bucket_spec(spec_id)
                    if result and result.get("success"):
                        st.success(f"✅ Bucket spec deleted successfully!")
                        st.rerun()
                    else:
                        st.error(f"❌ Failed to delete bucket spec: {result.get('message', 'Unknown error')}")
    else:
        st.info("No bucket versions available") 