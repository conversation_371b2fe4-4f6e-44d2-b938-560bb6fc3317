"""
EXPERIMENTAL VERSION - Copy of Resampled Data page for testing changes
This is a copy of the resampled_data.py file that can be modified without affecting the original.
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime
from ..data_cache import get_cached_bucket_versions, get_cached_bucket_resampled_data, get_cached_ndx_resampled_data, get_cached_backtest_latest_saved
from ..api_client import get_bucket_resampled_data, call_api
from ..utils import safe_dataframe_display, get_shared_date_state, create_shared_date_controls, display_executed_trades, display_trade_reports, get_vix_close_for_date
from core.config import settings

def create_spx_chart(df_filtered, selected_date):
    """Create the SPX chart with accumulated OHLC candlestick and volume"""
    if len(df_filtered) == 0:
        return None
    
    # Create bucket labels for x-axis with 30-second resolution
    bucket_labels = []
    for idx in df_filtered.index:
        # Format as bucket start time with seconds for 30-second resolution
        bucket_labels.append(idx.strftime('%H:%M:%S'))
    
    # Get the actual time range of the data
    data_start_time = df_filtered.index.min()
    data_end_time = df_filtered.index.max()
    
    # Add padding to prevent cutoff
    padding = pd.Timedelta(seconds=10)
    chart_start_time = data_start_time - padding
    chart_end_time = data_end_time + padding
    
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=('Accumulated OHLC (15:50:00 to current bar)', 'Volume'),
        row_width=[0.3, 0.7]
    )
    
    # Calculate accumulated OHLC values for each bar
    accumulated_ohlc = []
    hover_text = []
    table_data = []
    
    for i, idx in enumerate(df_filtered.index):
        bucket_start = idx.strftime('%H:%M:%S')
        # Try to get bucket end time from the next bucket or use a default
        if i < len(df_filtered.index) - 1:
            bucket_end = df_filtered.index[i + 1].strftime('%H:%M:%S')
        else:
            # For the last bucket, add 30 seconds as a reasonable estimate
            bucket_end = (idx + pd.Timedelta(seconds=30)).strftime('%H:%M:%S')
        
        # Get current bar values
        row = df_filtered.iloc[i]
        current_close = row['close']
        
        # Calculate accumulated values from 15:50:00 to this bar
        start_time = pd.to_datetime(f"{idx.date()} 15:50:00")
        accumulated_data = df_filtered[df_filtered.index >= start_time].loc[:idx]
        
        if not accumulated_data.empty:
            # Get the 15:50:00 bar (first bar in accumulated data)
            first_bar = accumulated_data.iloc[0]
            accumulated_open = first_bar['open']  # Open of 15:50:00 bar
            accumulated_high = accumulated_data['high'].max()  # Max high from 15:50:00 to current
            accumulated_low = accumulated_data['low'].min()    # Min low from 15:50:00 to current
            accumulated_close = current_close  # Close of current bar
        else:
            # Fallback if no accumulated data
            accumulated_open = row['open']
            accumulated_high = row['high']
            accumulated_low = row['low']
            accumulated_close = row['close']
        
        # Calculate accumulated span and percentage close
        accumulated_span = accumulated_high - accumulated_low
        if accumulated_span > 0:
            accumulated_pct_close = ((accumulated_close - accumulated_low) / accumulated_span * 100)
        else:
            accumulated_pct_close = 0
        
        # Store accumulated OHLC for chart
        accumulated_ohlc.append({
            'open': accumulated_open,
            'high': accumulated_high,
            'low': accumulated_low,
            'close': accumulated_close
        })
        
        # Store data for table
        table_data.append({
            'Time': bucket_start,
            'Acc. Open': f"${accumulated_open:.2f}",
            'Acc. High': f"${accumulated_high:.2f}",
            'Acc. Low': f"${accumulated_low:.2f}",
            'Acc. Close': f"${accumulated_close:.2f}",
            'Acc. Span': f"${accumulated_span:.2f}",
            'Acc. %Close': f"{accumulated_pct_close:.1f}%"
        })
        
        hover_text.append(
            f"Bucket: {bucket_start} - {bucket_end}<br>" +
            f"Accumulated OHLC (15:50:00 to {bucket_start}):<br>" +
            f"Open: ${accumulated_open:.2f} (15:50:00 bar)<br>" +
            f"High: ${accumulated_high:.2f} (max from 15:50:00)<br>" +
            f"Low: ${accumulated_low:.2f} (min from 15:50:00)<br>" +
            f"Close: ${accumulated_close:.2f} (current bar)<br>" +
            f"Span: ${accumulated_span:.2f}<br>" +
            f"% Close: {accumulated_pct_close:.1f}%"
        )
    
    fig.add_trace(
        go.Candlestick(
            x=bucket_labels,  # Use bucket labels instead of timestamps
            open=[ohlc['open'] for ohlc in accumulated_ohlc],
            high=[ohlc['high'] for ohlc in accumulated_ohlc],
            low=[ohlc['low'] for ohlc in accumulated_ohlc],
            close=[ohlc['close'] for ohlc in accumulated_ohlc],
            name="Accumulated OHLC",
            increasing_line_color='#26A69A',
            decreasing_line_color='#EF5350',
            hovertext=hover_text,
            hoverinfo='text'
        ),
        row=1, col=1
    )
    
    # Volume chart
    colors = ['#26A69A' if ohlc['close'] >= ohlc['open'] else '#EF5350' 
              for ohlc in accumulated_ohlc]
    
    fig.add_trace(
        go.Bar(
            x=bucket_labels,  # Use bucket labels instead of timestamps
            y=df_filtered['volume'],
            name="Volume",
            marker_color=colors
        ),
        row=2, col=1
    )
    
    # Update layout with bucket-based x-axis
    fig.update_layout(
        title="",
        xaxis_rangeslider_visible=False,
        height=600,
        showlegend=False,
        template="plotly_white",
        # Set X-axis to show bucket times
        xaxis=dict(
            type='category',  # Use category type for bucket labels
            tickmode='array',
            tickvals=bucket_labels,
            ticktext=bucket_labels,
            tickangle=45,
            # Add margin to prevent cutoff
            automargin=True
        ),
        xaxis2=dict(
            type='category',  # Use category type for bucket labels
            tickvals=bucket_labels,
            ticktext=bucket_labels,
            tickangle=45,
            # Add margin to prevent cutoff
            automargin=True
        )
    )
    
    # Update Y-axis for volume
    fig.update_yaxes(title_text="Price ($)", row=1, col=1)
    fig.update_yaxes(title_text="Volume", row=2, col=1)
    
    return fig, table_data

def display_backtest_trades(backtest_data, selected_date):
    """Display backtest trades in a reusable function"""
    if backtest_data is not None and not backtest_data.empty:
        # Convert trading_date to datetime for comparison
        backtest_data['trading_date'] = pd.to_datetime(backtest_data['trading_date'])
        
        # Filter backtest data for the selected date
        date_backtest = backtest_data[backtest_data['trading_date'].dt.date == selected_date]
        
        if not date_backtest.empty:
            # Use columns to make it more compact
            cols = st.columns(len(date_backtest))
            for i, (idx, trade) in enumerate(date_backtest.iterrows()):
                decision = trade.get('decision', 'Unknown')
                target_price = trade.get('target_price', 'N/A')
                # Format target_price as integer if it's a number
                if target_price != 'N/A' and target_price is not None:
                    try:
                        target_price = int(float(target_price))
                    except (ValueError, TypeError):
                        pass  # Keep as is if conversion fails
                spread_price = trade.get('spread_price', 'N/A')
                final_strikes = trade.get('final_strikes', 'N/A')
                
                with cols[i]:
                    with st.expander(f"Trade {i+1}: {decision} - {target_price}", expanded=False):
                        st.write(f"**Spread Price:** ${spread_price}")
                        st.write(f"**Final Strikes:** {final_strikes}")

def display_backtest_latest_saved_records(selected_date):
    """Display backtest_latest_saved records for the selected date as expanders"""
    backtest_data = get_cached_backtest_latest_saved()
    if not backtest_data or not backtest_data.get('data'):
        return
    import pandas as pd
    df = pd.DataFrame(backtest_data['data'])
    if df.empty or 'trading_date' not in df.columns:
        return
    df['trading_date'] = pd.to_datetime(df['trading_date'])
    date_records = df[df['trading_date'].dt.date == selected_date]
    if date_records.empty:
        return
    st.subheader("🧪 Backtest Records")
    fields_to_exclude = [
        'processing_time_seconds',
        'created_at',
        'narrow_fill_ohlc',
        'narrow_fill_consolidated_ohlc'
    ]
    for i, (_, record) in enumerate(date_records.iterrows()):
        title_parts = []
        # Compose a concise title from key fields
        for field in ['decision', 'target_price', 'spread_price', 'final_strikes']:
            val = record.get(field)
            if pd.notna(val) and val != 'N/A':
                title_parts.append(f"{field.replace('_',' ').title()}: {val}")
        expander_title = f"Backtest {i+1}: " + " | ".join(title_parts) if title_parts else f"Backtest {i+1}"
        with st.expander(expander_title, expanded=False):
            for col, val in record.items():
                if pd.notna(val) and col != 'trading_date' and col not in fields_to_exclude:
                    st.write(f"**{col.replace('_',' ').title()}:** {val}")

def show_spx_tab(selected_bucket_version, selected_date, backtest_data):
    """Show SPX data in a tab"""
    # Load data based on selected bucket version
    bucket_name = selected_bucket_version.get('version', 'unknown')
    bucket_data = get_cached_bucket_resampled_data(bucket_name, limit=10000000)
    if not bucket_data:
        st.error(f"Failed to load bucket resampled data for version: {bucket_name}")
        return
    
    df = pd.DataFrame(bucket_data["data"])
    if df.empty:
        st.warning(f"No bucket resampled data available for version: {bucket_name}")
        return
    
    # Handle timestamp column - check for both 'date' and 'timestamp' column names
    timestamp_col = None
    if 'timestamp' in df.columns:
        timestamp_col = 'timestamp'
    elif 'date' in df.columns:
        timestamp_col = 'date'
    else:
        st.error("No timestamp or date column found in bucket data")
        st.write("Available columns:", list(df.columns))
        return
    
    df[timestamp_col] = pd.to_datetime(df[timestamp_col])
    df.set_index(timestamp_col, inplace=True)
    
    # Get available dates - handle potential index issues
    try:
        available_dates = sorted(pd.Series(df.index.date).unique())
    except AttributeError as e:
        # If index doesn't have date attribute, try to convert it
        df.index = pd.to_datetime(df.index)
        available_dates = sorted(pd.Series(df.index.date).unique())
    
    if not available_dates:
        st.warning("No data available")
        return
    
    # Validate that selected_date is in available_dates
    if selected_date not in available_dates:
        st.warning(f"⚠️ No data available for {selected_date}.")
        return
    
    # Filter data for selected date (show all data for the day)
    try:
        df_daily = df[df.index.date == selected_date]
    except AttributeError:
        # If index doesn't have date attribute, ensure it's datetime
        df.index = pd.to_datetime(df.index)
        df_daily = df[df.index.date == selected_date]
    
    if df_daily.empty:
        st.warning(f"No data available for {selected_date}")
        return
    
    # --- Apply end-of-day time filtering ---
    start_time = settings.DEFAULT_START_TIME
    end_time = settings.DEFAULT_END_TIME
    df_filtered = df_daily.between_time(start_time, end_time)
    # --------------------------------------
    
    if df_filtered.empty:
        st.warning(f"No data available for {selected_date} in the end-of-day window")
        return
    
    # Display backtest decisions and trading details above the chart
    display_backtest_trades(backtest_data, selected_date)
    # Display trade reports for the selected date
    display_trade_reports(selected_date)
    # Display backtest_latest_saved records for the selected date
    display_backtest_latest_saved_records(selected_date)

    # Create and display the chart
    result = create_spx_chart(df_filtered, selected_date)
    if result:
        fig, table_data = result
        st.plotly_chart(fig, use_container_width=True, key="spx_experimental_chart")

        # Display the data table
        if table_data:
            st.subheader("📊 Data Table")
            df_table = pd.DataFrame(table_data)
            st.dataframe(df_table, use_container_width=True)

def show_ndx_tab(selected_bucket_version, selected_date, backtest_data):
    """Show NDX data in a tab"""
    # Load NDX data based on selected bucket version
    bucket_name = selected_bucket_version.get('version', 'unknown')
    ndx_data = get_cached_ndx_resampled_data(limit=10000000)
    if not ndx_data:
        st.error(f"Failed to load NDX resampled data")
        return
    
    df = pd.DataFrame(ndx_data["data"])
    if df.empty:
        st.warning("No NDX resampled data available")
        return
    
    # Handle timestamp column - check for both 'date' and 'timestamp' column names
    timestamp_col = None
    if 'timestamp' in df.columns:
        timestamp_col = 'timestamp'
    elif 'date' in df.columns:
        timestamp_col = 'date'
    else:
        st.error("No timestamp or date column found in NDX data")
        st.write("Available columns:", list(df.columns))
        return
    
    df[timestamp_col] = pd.to_datetime(df[timestamp_col])
    df.set_index(timestamp_col, inplace=True)
    
    # Get available dates - handle potential index issues
    try:
        available_dates = sorted(pd.Series(df.index.date).unique())
    except AttributeError as e:
        df.index = pd.to_datetime(df.index)
        available_dates = sorted(pd.Series(df.index.date).unique())
    
    if not available_dates:
        st.warning("No NDX data available")
        return
    
    # Validate that selected_date is in available_dates
    if selected_date not in available_dates:
        st.warning(f"⚠️ No NDX data available for {selected_date}.")
        return
    
    # Filter data for selected date (show all data for the day)
    try:
        df_daily = df[df.index.date == selected_date]
    except AttributeError:
        df.index = pd.to_datetime(df.index)
        df_daily = df[df.index.date == selected_date]
    
    if df_daily.empty:
        st.warning(f"No NDX data available for {selected_date}")
        return
    
    # --- Apply end-of-day time filtering ---
    start_time = settings.DEFAULT_START_TIME
    end_time = settings.DEFAULT_END_TIME
    df_filtered = df_daily.between_time(start_time, end_time)
    # --------------------------------------
    
    if df_filtered.empty:
        st.warning(f"No NDX data available for {selected_date} in the end-of-day window")
        return
    
    # Display backtest decisions and trading details above the chart
    display_backtest_trades(backtest_data, selected_date)
    # Display trade reports for the selected date
    display_trade_reports(selected_date)
    # Display backtest_latest_saved records for the selected date
    display_backtest_latest_saved_records(selected_date)

    result = create_spx_chart(df_filtered, selected_date)
    if result:
        fig, table_data = result
        st.plotly_chart(fig, use_container_width=True, key="ndx_experimental_chart")

        # Display the data table
        if table_data:
            st.subheader("📊 Data Table")
            df_table = pd.DataFrame(table_data)
            st.dataframe(df_table, use_container_width=True)




def show_experimental():
    """Show experimental data page"""
    # Add custom CSS for compact styling
    st.markdown("""
        <style>
        .compact-header {
            font-size: 1.2em !important;
            margin-bottom: 0.5em !important;
        }
        .stDataFrame {
            margin-top: 0.5em !important;
        }
        .stSelectbox > div > div {
            padding: 0.25rem 0.5rem !important;
        }
        .stTabs [data-baseweb="tab-list"] {
            gap: 0.5rem !important;
        }
        .stTabs [data-baseweb="tab"] {
            padding: 0.5rem 1rem !important;
        }
        .stButton > button {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.8rem !important;
        }
        </style>
        """, unsafe_allow_html=True)
    
    # Get bucket versions for selection
    bucket_versions = get_cached_bucket_versions()
    available_bucket_versions = []
    if bucket_versions and bucket_versions.get('data'):
        available_bucket_versions = bucket_versions['data']
    
    bucket_version_options = available_bucket_versions
    
    # Get shared date state
    selected_date, shared_available_dates = get_shared_date_state()

    # Create a row with bucket version selector and navigation
    col1, col2 = st.columns([1, 2])
    
    with col1:
        # Bucket version selector
        selected_bucket_version = st.selectbox(
            "Select Bucket Version:",
            options=bucket_version_options,
            format_func=lambda x: x.get('version', 'Unknown'),
            key="experimental_bucket_version_selector"
        )
    
    with col2:
        # Date controls (shared across pages)
        create_shared_date_controls(selected_date, shared_available_dates, "experimental")
    
    # No backtest data needed
    backtest_data = None
    
    # Create tabs for different tickers
    tab1, tab2 = st.tabs(["📈 SPX", "📊 NDX"])
    
    with tab1:
        show_spx_tab(selected_bucket_version, selected_date, backtest_data)
    
    with tab2:
        show_ndx_tab(selected_bucket_version, selected_date, backtest_data)

