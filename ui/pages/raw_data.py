import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from ..data_cache import get_cached_raw_data, get_cached_default_time_range, get_cached_ndx_raw_data, get_cached_backtest_latest_saved
from ..api_client import get_bucket_resampled_data, call_api
from ..utils import get_shared_date_state, create_shared_date_controls, display_executed_trades, display_trade_reports, get_vix_close_for_date
from core.config import settings

def create_raw_chart(df_filtered, selected_date, start_time, end_time):
    """Create the raw data chart with candlestick and volume"""
    if len(df_filtered) == 0:
        return None
    
    # Create time labels for x-axis
    time_labels = [idx.strftime('%H:%M:%S') for idx in df_filtered.index]
    
    # Get the actual time range of the data
    data_start_time = df_filtered.index.min()
    data_end_time = df_filtered.index.max()
    
    # Add padding to prevent cutoff
    padding = pd.Timedelta(seconds=10)
    chart_start_time = data_start_time - padding
    chart_end_time = data_end_time + padding
    
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=(f'{selected_date} ({start_time} to {end_time})', 'Volume'),
        row_width=[0.3, 0.7]
    )
    
    # Candlestick chart
    # Calculate span and %close for tooltips
    span = df_filtered['high'] - df_filtered['low']
    pct_close = ((df_filtered['close'] - df_filtered['low']) / span * 100).fillna(0)
    
    # Create custom hover text
    hover_text = []
    for i, (idx, s, p) in enumerate(zip(df_filtered.index, span, pct_close)):
        # Get OHLC values for this row
        row = df_filtered.iloc[i]
        open_val = row['open']
        high_val = row['high']
        low_val = row['low']
        close_val = row['close']
        
        hover_text.append(
            f"Time: {idx.strftime('%H:%M:%S')}<br>" +
            f"Open: ${open_val:.2f}<br>" +
            f"High: ${high_val:.2f}<br>" +
            f"Low: ${low_val:.2f}<br>" +
            f"Close: ${close_val:.2f}<br>" +
            f"Span: ${s:.2f}<br>" +
            f"%Close: {p:.1f}%"
        )
    
    fig.add_trace(
        go.Candlestick(
            x=time_labels,
            open=df_filtered['open'],
            high=df_filtered['high'],
            low=df_filtered['low'],
            close=df_filtered['close'],
            name="OHLC",
            increasing_line_color='#26A69A',
            decreasing_line_color='#EF5350',
            hovertext=hover_text,
            hoverinfo='text'
        ),
        row=1, col=1
    )
    
    # Volume chart
    colors = ['#26A69A' if close >= open else '#EF5350' 
              for close, open in zip(df_filtered['close'], df_filtered['open'])]
    
    fig.add_trace(
        go.Bar(
            x=time_labels,
            y=df_filtered['volume'],
            name="Volume",
            marker_color=colors
        ),
        row=2, col=1
    )
    
    # Update layout
    fig.update_layout(
        title="",
        xaxis_rangeslider_visible=False,
        height=600,
        showlegend=False,
        template="plotly_white"
    )
    
    # Update Y-axis for volume
    fig.update_yaxes(title_text="Price ($)", row=1, col=1)
    fig.update_yaxes(title_text="Volume", row=2, col=1)
    
    return fig

def display_backtest_trades(backtest_data, selected_date):
    """Display backtest trades in a reusable function"""
    if backtest_data is not None and not backtest_data.empty:
        # Convert trading_date to datetime for comparison
        backtest_data['trading_date'] = pd.to_datetime(backtest_data['trading_date'])
        
        # Filter backtest data for the selected date
        date_backtest = backtest_data[backtest_data['trading_date'].dt.date == selected_date]
        
        if not date_backtest.empty:
            # Use columns to make it more compact
            cols = st.columns(len(date_backtest))
            for i, (idx, trade) in enumerate(date_backtest.iterrows()):
                decision = trade.get('decision', 'Unknown')
                target_price = trade.get('target_price', 'N/A')
                # Format target_price as integer if it's a number
                if target_price != 'N/A' and target_price is not None:
                    try:
                        target_price = int(float(target_price))
                    except (ValueError, TypeError):
                        pass  # Keep as is if conversion fails
                spread_price = trade.get('spread_price', 'N/A')
                final_strikes = trade.get('final_strikes', 'N/A')
                
                with cols[i]:
                    with st.expander(f"Trade {i+1}: {decision} - {target_price}", expanded=False):
                        st.write(f"**Spread Price:** ${spread_price}")
                        st.write(f"**Final Strikes:** {final_strikes}")

def display_backtest_latest_saved_records(selected_date):
    """Display backtest_latest_saved records for the selected date as expanders"""
    backtest_data = get_cached_backtest_latest_saved()
    if not backtest_data or not backtest_data.get('data'):
        return
    import pandas as pd
    df = pd.DataFrame(backtest_data['data'])
    if df.empty or 'trading_date' not in df.columns:
        return
    df['trading_date'] = pd.to_datetime(df['trading_date'])
    date_records = df[df['trading_date'].dt.date == selected_date]
    if date_records.empty:
        return
    st.subheader("🧪 Backtest Records")
    fields_to_exclude = [
        'processing_time_seconds',
        'created_at',
        'narrow_fill_ohlc',
        'narrow_fill_consolidated_ohlc'
    ]
    for i, (_, record) in enumerate(date_records.iterrows()):
        title_parts = []
        # Compose a concise title from key fields
        for field in ['decision', 'target_price', 'spread_price', 'final_strikes']:
            val = record.get(field)
            if pd.notna(val) and val != 'N/A':
                title_parts.append(f"{field.replace('_',' ').title()}: {val}")
        expander_title = f"Backtest {i+1}: " + " | ".join(title_parts) if title_parts else f"Backtest {i+1}"
        with st.expander(expander_title, expanded=False):
            for col, val in record.items():
                if pd.notna(val) and col != 'trading_date' and col not in fields_to_exclude:
                    st.write(f"**{col.replace('_',' ').title()}:** {val}")

def show_spx_raw_tab(selected_date, start_time, end_time, backtest_data, raw_data=None):
    """Show SPX raw data in a tab"""
    # Use provided raw_data or get it if not provided
    if raw_data is None:
        raw_data = get_cached_raw_data(limit=50000)  # Get more data for date selection
        if not raw_data:
            st.error("Failed to load SPX 10s data")
            return
    
    # Convert to DataFrame and handle missing values
    raw_df = pd.DataFrame(raw_data["data"])
    raw_df = raw_df.fillna(0).infer_objects(copy=False)
    
    # Ensure date column exists and convert to datetime
    if 'date' in raw_df.columns:
        raw_df['date'] = pd.to_datetime(raw_df['date'])
        raw_df.set_index('date', inplace=True)
    
    # Get available dates
    available_dates = sorted(pd.Series(raw_df.index.date).unique())
    
    if not available_dates:
        st.warning("No SPX data available")
        return
    
    # Filter data for selected date and time range
    try:
        df_daily = raw_df[raw_df.index.date == selected_date]
    except AttributeError:
        # If index doesn't have date attribute, ensure it's datetime
        raw_df.index = pd.to_datetime(raw_df.index)
        df_daily = raw_df[raw_df.index.date == selected_date]
    
    if df_daily.empty:
        st.warning(f"No SPX data available for {selected_date}")
        return
    
    # Filter by time range
    df_filtered = df_daily.between_time(start_time, end_time)
    
    if df_filtered.empty:
        st.warning(f"No SPX data available for {selected_date} between {start_time} and {end_time}")
        return
    
    # Display backtest decisions and trading details above the chart
    display_backtest_trades(backtest_data, selected_date)
    # Display trade reports for the selected date
    display_trade_reports(selected_date)
    # Display backtest_latest_saved records for the selected date
    display_backtest_latest_saved_records(selected_date)

    # Display VIX close above the chart
    vix_close = get_vix_close_for_date(selected_date)
    vix_display = f"VIX Close: {vix_close:.2f}" if vix_close is not None else "VIX Close: N/A"
    st.markdown(f"**{vix_display}**")

    # Create and display the chart
    fig = create_raw_chart(df_filtered, selected_date, start_time, end_time)
    if fig:
        st.plotly_chart(fig, use_container_width=True, key="spx_raw_data_chart")

def show_ndx_raw_tab(selected_date, start_time, end_time, backtest_data):
    """Show NDX raw data in a tab"""
    # Load NDX raw data
    ndx_data = get_cached_ndx_raw_data(limit=50000)
    if not ndx_data:
        st.error("Failed to load NDX 10s data")
        return
    
    # Convert to DataFrame and handle missing values
    raw_df = pd.DataFrame(ndx_data["data"])
    raw_df = raw_df.fillna(0).infer_objects(copy=False)
    
    # Ensure date column exists and convert to datetime
    if 'date' in raw_df.columns:
        raw_df['date'] = pd.to_datetime(raw_df['date'])
        raw_df.set_index('date', inplace=True)
    
    # Get available dates
    available_dates = sorted(pd.Series(raw_df.index.date).unique())
    
    if not available_dates:
        st.warning("No NDX data available")
        return
    
    # Filter data for selected date and time range
    try:
        df_daily = raw_df[raw_df.index.date == selected_date]
    except AttributeError:
        # If index doesn't have date attribute, ensure it's datetime
        raw_df.index = pd.to_datetime(raw_df.index)
        df_daily = raw_df[raw_df.index.date == selected_date]
    
    if df_daily.empty:
        st.warning(f"No NDX data available for {selected_date}")
        return
    
    # Filter by time range
    df_filtered = df_daily.between_time(start_time, end_time)
    
    if df_filtered.empty:
        st.warning(f"No NDX data available for {selected_date} between {start_time} and {end_time}")
        return
    
    # Display backtest decisions and trading details above the chart
    display_backtest_trades(backtest_data, selected_date)

    # Display executed trades for the selected date
    display_executed_trades(selected_date)

    # Display trade reports for the selected date
    display_trade_reports(selected_date)
    # Display backtest_latest_saved records for the selected date
    display_backtest_latest_saved_records(selected_date)

    # Display VIX close above the chart
    vix_close = get_vix_close_for_date(selected_date)
    vix_display = f"VIX Close: {vix_close:.2f}" if vix_close is not None else "VIX Close: N/A"
    st.markdown(f"**{vix_display}**")

    # Create and display the chart
    fig = create_raw_chart(df_filtered, selected_date, start_time, end_time)
    if fig:
        st.plotly_chart(fig, use_container_width=True, key="ndx_raw_data_chart")

def show_raw_data():
    """Show 10s data page with tabs for different tickers"""
    # Get all raw data first for date selection (using SPX data)
    raw_data = get_cached_raw_data(limit=50000)  # Get more data for date selection
    if not raw_data:
        st.error("Failed to load 10s data")
        return

    # Convert to DataFrame and handle missing values
    raw_df = pd.DataFrame(raw_data["data"])
    raw_df = raw_df.fillna(0).infer_objects(copy=False)

    # Ensure date column exists and convert to datetime
    if 'date' in raw_df.columns:
        raw_df['date'] = pd.to_datetime(raw_df['date'])
        raw_df.set_index('date', inplace=True)

    # Get available dates
    available_dates = sorted(pd.Series(raw_df.index.date).unique())
    
    if not available_dates:
        st.warning("No data available")
        return
    
    # Get shared date state
    selected_date, shared_available_dates = get_shared_date_state()

    # Date controls (shared across pages)
    create_shared_date_controls(selected_date, shared_available_dates, "raw")
    
    # Use default time range settings
    time_settings = get_cached_default_time_range()
    start_time = pd.to_datetime(time_settings.get("default_start_time", "15:45:00")).time() if time_settings else pd.to_datetime("15:45:00").time()
    end_time = pd.to_datetime(time_settings.get("default_end_time", "16:00:00")).time() if time_settings else pd.to_datetime("16:00:00").time()
    
    # No backtest data needed
    backtest_data = None
    
    # Create tabs for different tickers
    tab1, tab2 = st.tabs(["📈 SPX", "📊 NDX"])
    
    with tab1:
        show_spx_raw_tab(selected_date, start_time, end_time, backtest_data, raw_data)

    with tab2:
        show_ndx_raw_tab(selected_date, start_time, end_time, backtest_data)
