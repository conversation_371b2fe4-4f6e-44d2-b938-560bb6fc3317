import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from ..data_cache import get_cached_raw_data
from plotly.subplots import make_subplots

def show_insights():
    """Show insights page with various analytics"""
    
    # Get raw data
    raw_data = get_cached_raw_data(limit=None)  # Get all data for comprehensive analysis
    if not raw_data:
        st.error("Failed to load raw data for analysis")
        return
    
    # Convert to DataFrame
    raw_df = pd.DataFrame(raw_data["data"])
    if raw_df.empty:
        st.warning("No raw data available for analysis")
        return
    
    # Ensure date column exists and convert to datetime
    if 'date' in raw_df.columns:
        raw_df['date'] = pd.to_datetime(raw_df['date'])
        raw_df.set_index('date', inplace=True)
    else:
        st.error("No date column found in raw data")
        return
    
    # Calculate span (high - low)
    if 'high' in raw_df.columns and 'low' in raw_df.columns:
        raw_df['span'] = raw_df['high'] - raw_df['low']
    else:
        st.error("High and/or Low columns not found in raw data")
        return
    
    # Add day of week and time components
    raw_df['day_of_week'] = raw_df.index.day_name()
    raw_df['time'] = raw_df.index.time
    raw_df['date_only'] = raw_df.index.date
    
    # Define the time bins we're interested in (15:59:00 to 15:59:50, every 10 seconds)
    target_times = [
        pd.to_datetime('15:59:00').time(),
        pd.to_datetime('15:59:10').time(),
        pd.to_datetime('15:59:20').time(),
        pd.to_datetime('15:59:30').time(),
        pd.to_datetime('15:59:40').time(),
        pd.to_datetime('15:59:50').time()
    ]
    
    # Filter data for only the target time bins
    time_data = raw_df[raw_df['time'].isin(target_times)].copy()
    
    if time_data.empty:
        st.warning("No data found for the specified time bins (15:59:00-15:59:50)")
        return
    
    # Create subplots for all analyses
    fig = make_subplots(
        rows=1, cols=3,
        subplot_titles=('15:59:00 (bins 00,10,20)', '15:59:30 (bins 30,40,50)', '15:59 (all 6 bins)'),
        specs=[[{"type": "bar"}, {"type": "bar"}, {"type": "bar"}]]
    )
    
    # Analysis 1: 15:59:00 (bins 00, 10, 20)
    bins_00_10_20 = [
        pd.to_datetime('15:59:00').time(),
        pd.to_datetime('15:59:10').time(),
        pd.to_datetime('15:59:20').time()
    ]
    
    data_00_10_20 = time_data[time_data['time'].isin(bins_00_10_20)].copy()
    
    if not data_00_10_20.empty:
        # Group by date and calculate max(high) and min(low) for each day
        daily_stats_00_10_20 = data_00_10_20.groupby('date_only').agg({
            'high': 'max',
            'low': 'min'
        })
        
        # Calculate the true span for each day: max(high) - min(low)
        daily_stats_00_10_20['span'] = daily_stats_00_10_20['high'] - daily_stats_00_10_20['low']
        
        # Add day of week to the daily stats
        daily_stats_00_10_20.index = pd.to_datetime(daily_stats_00_10_20.index)
        daily_stats_00_10_20['day_of_week'] = daily_stats_00_10_20.index.day_name()
        daily_stats_00_10_20 = daily_stats_00_10_20.reset_index()
        
        # Calculate average span by day of week
        day_analysis_00_10_20 = daily_stats_00_10_20.groupby('day_of_week')['span'].agg(['mean', 'count', 'std']).round(2)
        day_analysis_00_10_20 = day_analysis_00_10_20.reset_index()
        
        # Reorder days to start with Monday
        day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        day_analysis_00_10_20['day_of_week'] = pd.Categorical(day_analysis_00_10_20['day_of_week'], categories=day_order, ordered=True)
        day_analysis_00_10_20 = day_analysis_00_10_20.sort_values('day_of_week')
        
        # Add bar chart to subplot
        fig.add_trace(
            go.Bar(
                x=day_analysis_00_10_20['day_of_week'],
                y=day_analysis_00_10_20['mean'],
                name='15:59:00 (bins 00,10,20)',
                error_y=dict(type='data', array=day_analysis_00_10_20['std'], visible=True),
                marker_color='#1f77b4',
                showlegend=False
            ),
            row=1, col=1
        )
    
    # Analysis 2: 15:59:30 (bins 30, 40, 50)
    bins_30_40_50 = [
        pd.to_datetime('15:59:30').time(),
        pd.to_datetime('15:59:40').time(),
        pd.to_datetime('15:59:50').time()
    ]
    
    data_30_40_50 = time_data[time_data['time'].isin(bins_30_40_50)].copy()
    
    if not data_30_40_50.empty:
        # Group by date and calculate max(high) and min(low) for each day
        daily_stats_30_40_50 = data_30_40_50.groupby('date_only').agg({
            'high': 'max',
            'low': 'min'
        })
        
        # Calculate the true span for each day: max(high) - min(low)
        daily_stats_30_40_50['span'] = daily_stats_30_40_50['high'] - daily_stats_30_40_50['low']
        
        # Add day of week to the daily stats
        daily_stats_30_40_50.index = pd.to_datetime(daily_stats_30_40_50.index)
        daily_stats_30_40_50['day_of_week'] = daily_stats_30_40_50.index.day_name()
        daily_stats_30_40_50 = daily_stats_30_40_50.reset_index()
        
        # Calculate average span by day of week
        day_analysis_30_40_50 = daily_stats_30_40_50.groupby('day_of_week')['span'].agg(['mean', 'count', 'std']).round(2)
        day_analysis_30_40_50 = day_analysis_30_40_50.reset_index()
        
        # Reorder days to start with Monday
        day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        day_analysis_30_40_50['day_of_week'] = pd.Categorical(day_analysis_30_40_50['day_of_week'], categories=day_order, ordered=True)
        day_analysis_30_40_50 = day_analysis_30_40_50.sort_values('day_of_week')
        
        # Add bar chart to subplot
        fig.add_trace(
            go.Bar(
                x=day_analysis_30_40_50['day_of_week'],
                y=day_analysis_30_40_50['mean'],
                name='15:59:30 (bins 30,40,50)',
                error_y=dict(type='data', array=day_analysis_30_40_50['std'], visible=True),
                marker_color='#ff7f0e',
                showlegend=False
            ),
            row=1, col=2
        )
    
    # Analysis 3: 15:59 (all 6 bins: 00, 10, 20, 30, 40, 50)
    if not time_data.empty:
        # Group by date and calculate max(high) and min(low) for each day across all 6 bins
        daily_stats_all = time_data.groupby('date_only').agg({
            'high': 'max',
            'low': 'min'
        })
        
        # Calculate the true span for each day: max(high) - min(low)
        daily_stats_all['span'] = daily_stats_all['high'] - daily_stats_all['low']
        
        # Add day of week to the daily stats
        daily_stats_all.index = pd.to_datetime(daily_stats_all.index)
        daily_stats_all['day_of_week'] = daily_stats_all.index.day_name()
        daily_stats_all = daily_stats_all.reset_index()
        
        # Calculate average span by day of week
        day_analysis_all = daily_stats_all.groupby('day_of_week')['span'].agg(['mean', 'count', 'std']).round(2)
        day_analysis_all = day_analysis_all.reset_index()
        
        # Reorder days to start with Monday
        day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        day_analysis_all['day_of_week'] = pd.Categorical(day_analysis_all['day_of_week'], categories=day_order, ordered=True)
        day_analysis_all = day_analysis_all.sort_values('day_of_week')
        
        # Add bar chart to subplot
        fig.add_trace(
            go.Bar(
                x=day_analysis_all['day_of_week'],
                y=day_analysis_all['mean'],
                name='15:59 (all 6 bins)',
                error_y=dict(type='data', array=day_analysis_all['std'], visible=True),
                marker_color='#2ca02c',
                showlegend=False
            ),
            row=1, col=3
        )
    
    # Update layout
    fig.update_layout(
        title="Average Span by Day of Week - Time Bin Analysis",
        height=500,
        showlegend=False,
        template="plotly_white"
    )
    
    # Update all subplot axes
    for i in range(1, 4):
        fig.update_xaxes(tickangle=-45, row=1, col=i)
        fig.update_yaxes(title_text="Average Span ($)", row=1, col=i)
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Display detailed statistics tables
    st.subheader("Detailed Statistics")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if 'day_analysis_00_10_20' in locals():
            st.write("**15:59:00 (bins 00,10,20)**")
            st.dataframe(day_analysis_00_10_20.set_index('day_of_week'))
    
    with col2:
        if 'day_analysis_30_40_50' in locals():
            st.write("**15:59:30 (bins 30,40,50)**")
            st.dataframe(day_analysis_30_40_50.set_index('day_of_week'))
    
    with col3:
        if 'day_analysis_all' in locals():
            st.write("**15:59 (all 6 bins)**")
            st.dataframe(day_analysis_all.set_index('day_of_week')) 