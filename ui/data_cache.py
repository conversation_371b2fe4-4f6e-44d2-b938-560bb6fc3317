import streamlit as st
from datetime import datetime
from .api_client import call_api

def load_all_data_once():
    """Load all data once and cache in session state"""
    # Check if already loading to prevent multiple simultaneous calls
    if 'data_loading' in st.session_state and st.session_state.data_loading:
        # Show loading indicator while waiting
        with st.spinner("🔄 Loading cached data..."):
            import time
            time.sleep(0.1)  # Brief pause to show spinner
        return

    # Check if data is already loaded (keep executed_trades check for compatibility)
    if ('raw_data' in st.session_state and
        'resampled_data' in st.session_state and
        'bucket_versions' in st.session_state and
        'executed_trades' in st.session_state and
        'trade_reports' in st.session_state and
        'cache_status' in st.session_state and
        'default_time_range' in st.session_state):
        return

    # Set loading flag
    st.session_state.data_loading = True

    try:
        with st.spinner("🔄 Loading data from cache..."):
            # Load data from API - optimized to minimize redundant calls
            # First, load the main data summary which triggers the primary cache load
            st.session_state.data_summary = call_api("/data/summary")

            # Now load individual datasets with reduced limits for faster initial load
            st.session_state.raw_data = call_api("/data/raw?limit=10000")  # Reduced from 50000
            st.session_state.resampled_data = call_api("/data/resampled?limit=10000")  # Reduced from 50000
            st.session_state.bucket_versions = call_api("/data/bucket-versions")
            st.session_state.executed_trades = call_api("/data/executed-trades")  # Load upfront for now
            st.session_state.trade_reports = call_api("/data/trade-reports")  # Load upfront for now
            st.session_state.cache_status = call_api("/data/status")
            st.session_state.default_time_range = call_api("/data/default-time-range")

            # Check if data was loaded successfully (silent check)
            if not st.session_state.raw_data:
                st.error("❌ Failed to load raw data")
            if not st.session_state.resampled_data:
                st.error("❌ Failed to load resampled data")
            if not st.session_state.bucket_versions:
                st.error("❌ Failed to load bucket versions")
    finally:
        # Clear loading flag
        st.session_state.data_loading = False

def get_cached_raw_data(limit=1000):
    """Get raw data from session state cache"""
    load_all_data_once()

    # If requesting more data than we have cached, load more
    if (limit and limit > 10000 and
        st.session_state.raw_data and
        len(st.session_state.raw_data.get('data', [])) < limit):
        with st.spinner(f"🔄 Loading {limit} rows of raw data..."):
            st.session_state.raw_data = call_api(f"/data/raw?limit={limit}")

    if st.session_state.raw_data:
        # Return limited data if requested
        if limit is not None and limit < len(st.session_state.raw_data.get('data', [])):
            limited_data = st.session_state.raw_data.copy()
            limited_data['data'] = st.session_state.raw_data['data'][-limit:]
            return limited_data
        return st.session_state.raw_data
    return None

def get_cached_resampled_data(limit=1000):
    """Get resampled data from session state cache"""
    load_all_data_once()

    # If requesting more data than we have cached, load more
    if (limit and limit > 10000 and
        st.session_state.resampled_data and
        len(st.session_state.resampled_data.get('data', [])) < limit):
        with st.spinner(f"🔄 Loading {limit} rows of resampled data..."):
            st.session_state.resampled_data = call_api(f"/data/resampled?limit={limit}")

    if st.session_state.resampled_data:
        # Return limited data if requested
        if limit < len(st.session_state.resampled_data.get('data', [])):
            limited_data = st.session_state.resampled_data.copy()
            limited_data['data'] = st.session_state.resampled_data['data'][-limit:]
            return limited_data
        return st.session_state.resampled_data
    return None

def get_cached_bucket_versions():
    """Get bucket versions from session state cache"""
    load_all_data_once()
    return st.session_state.bucket_versions if st.session_state.bucket_versions else None



def get_cached_executed_trades(date=None):
    """Get executed trades from session state cache"""
    load_all_data_once()

    if not st.session_state.executed_trades:
        return None

    if date and st.session_state.executed_trades:
        # Filter by date using entry_time (primary) or fallback to other date fields
        import pandas as pd

        filtered_records = []
        for record in st.session_state.executed_trades.get('data', []):
            # Primary: Use entry_time to extract date
            if 'entry_time' in record and record['entry_time']:
                try:
                    entry_date = pd.to_datetime(record['entry_time']).strftime('%Y-%m-%d')
                    if entry_date == str(date):
                        filtered_records.append(record)
                        continue
                except:
                    pass

            # Fallback: Check other date fields
            if (str(record.get('date', '')).startswith(str(date)) or
                str(record.get('trading_date', '')).startswith(str(date))):
                filtered_records.append(record)

        filtered_data = {'data': filtered_records}
        return filtered_data

    return st.session_state.executed_trades


def get_cached_trade_reports(date=None):
    """Get trade reports from session state cache"""
    load_all_data_once()

    if not st.session_state.trade_reports:
        return None

    # If no date filter, return all data
    if date is None:
        return st.session_state.trade_reports

    # Filter by date if provided
    try:
        import pandas as pd

        data = st.session_state.trade_reports.get('data', [])
        if not data:
            return st.session_state.trade_reports

        # Convert date to string format for comparison
        if isinstance(date, str):
            target_date = date
        else:
            target_date = date.strftime('%Y-%m-%d')

        # Filter data by date - check multiple possible date columns
        filtered_data = []
        for item in data:
            item_date = None

            # Try different date column names (prioritize session_date for trade_reports)
            if 'session_date' in item and item['session_date']:
                try:
                    item_date = pd.to_datetime(item['session_date']).strftime('%Y-%m-%d')
                except:
                    # If pandas conversion fails, try string conversion
                    item_date = str(item['session_date'])[:10]  # Take first 10 chars (YYYY-MM-DD)
            elif 'date' in item and item['date']:
                try:
                    item_date = pd.to_datetime(item['date']).strftime('%Y-%m-%d')
                except:
                    item_date = str(item['date'])[:10]
            elif 'created_at' in item and item['created_at']:
                try:
                    item_date = pd.to_datetime(item['created_at']).strftime('%Y-%m-%d')
                except:
                    item_date = str(item['created_at'])[:10]

            if item_date == target_date:
                filtered_data.append(item)

        # Return filtered data in the same format
        filtered_result = st.session_state.trade_reports.copy()
        filtered_result['data'] = filtered_data
        return filtered_result

    except Exception as e:
        import logging
        logging.getLogger(__name__).error(f"Error filtering trade reports by date: {str(e)}")
        return st.session_state.trade_reports

    return st.session_state.trade_reports


def get_cached_bucket_resampled_datasets():
    """Get bucket resampled datasets from session state cache"""
    load_all_data_once()
    return st.session_state.bucket_resampled_datasets

def get_cached_bucket_resampled_data(bucket_name, limit=1000):
    """Get bucket resampled data from session state cache with efficient caching"""
    load_all_data_once()

    # Create a cache key for this specific request
    cache_key = f"bucket_resampled_{bucket_name}_{limit}"

    if cache_key not in st.session_state:
        with st.spinner(f"🔄 Loading bucket resampled data for {bucket_name}..."):
            st.session_state[cache_key] = call_api(f"/data/bucket-resampled/{bucket_name}?limit={limit}")

    return st.session_state[cache_key]

def get_cached_ndx_resampled_data(limit=10000):
    """Get NDX resampled data from session state cache with efficient caching"""
    load_all_data_once()

    # Create a cache key for this specific request
    cache_key = f"ndx_resampled_{limit}"

    if cache_key not in st.session_state:
        with st.spinner("🔄 Loading NDX resampled data..."):
            st.session_state[cache_key] = call_api(f"/data/ndx-resampled?limit={limit}")

    return st.session_state[cache_key]

def get_cached_ndx_raw_data(limit=50000):
    """Get NDX raw data from session state cache with efficient caching"""
    load_all_data_once()

    # Create a cache key for this specific request
    cache_key = f"ndx_raw_{limit}"

    if cache_key not in st.session_state:
        with st.spinner("🔄 Loading NDX raw data..."):
            st.session_state[cache_key] = call_api(f"/data/ndx?limit={limit}")

    return st.session_state[cache_key]



def get_cached_vix_raw_data(limit=50000):
    """Get VIX raw data from session state cache with efficient caching"""
    load_all_data_once()

    # Create a cache key for this specific request
    cache_key = f"vix_raw_{limit}"

    if cache_key not in st.session_state:
        with st.spinner("🔄 Loading VIX raw data..."):
            st.session_state[cache_key] = call_api(f"/data/vix?limit={limit}")

    return st.session_state[cache_key]

def get_cached_data_summary():
    """Get data summary from session state cache"""
    load_all_data_once()
    return st.session_state.data_summary

def get_cached_cache_status():
    """Get cache status from session state cache"""
    load_all_data_once()
    return st.session_state.cache_status

def get_cached_default_time_range():
    """Get default time range from session state cache"""
    load_all_data_once()
    return st.session_state.default_time_range

def get_cached_available_dates(bucket_name=None):
    """Get available dates from session state cache"""
    cache_key = f"available_dates_{bucket_name}" if bucket_name else "available_dates_default"

    # Check if already cached
    if cache_key in st.session_state:
        return st.session_state[cache_key]

    # Fallback to using raw data for available dates (more reliable)
    try:
        # Use raw data to get available dates as fallback
        if 'raw_data' in st.session_state and st.session_state.raw_data:
            import pandas as pd
            raw_data = st.session_state.raw_data
            if raw_data.get('data'):
                df = pd.DataFrame(raw_data['data'])
                if not df.empty and 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    available_dates = sorted(pd.Series(df['date'].dt.date).unique())

                    # Cache the result
                    st.session_state[cache_key] = available_dates
                    return available_dates

        # If raw data approach fails, try the bucket endpoint
        if bucket_name:
            endpoint = f"/data/available-dates?bucket_name={bucket_name}"
        else:
            # Get default bucket version - but don't call get_cached_bucket_versions to avoid circular dependency
            if 'bucket_versions' in st.session_state and st.session_state.bucket_versions:
                bucket_versions = st.session_state.bucket_versions
                if bucket_versions.get('data'):
                    default_bucket = bucket_versions['data'][0].get('version', 'unknown')
                    endpoint = f"/data/available-dates?bucket_name={default_bucket}"
                else:
                    return []
            else:
                # If bucket versions not loaded yet, return empty list
                return []

        result = call_api(endpoint)
        dates = []
        if result and result.get('dates'):
            from datetime import datetime
            dates = [datetime.strptime(date_str, '%Y-%m-%d').date() for date_str in result['dates']]

        # Cache the result
        st.session_state[cache_key] = dates
        return dates

    except Exception as e:
        st.error(f"Error loading available dates: {str(e)}")
        return []

def get_cached_bucket_specs(bucket_version_id=None):
    """Get bucket specs from session state cache"""
    load_all_data_once()
    
    # Load bucket specs if not already cached
    if 'bucket_specs' not in st.session_state:
        st.session_state.bucket_specs = call_api("/data/bucket-specs")
    
    if bucket_version_id and st.session_state.bucket_specs:
        # Filter by bucket_version_id
        filtered_data = {
            'data': [
                spec for spec in st.session_state.bucket_specs.get('data', [])
                if spec.get('bucket_version_id') == bucket_version_id
            ]
        }
        return filtered_data
    
    return st.session_state.bucket_specs 

def clear_data_cache():
    """Clear all cached data from session state"""
    keys_to_clear = [
        'raw_data', 'resampled_data', 'bucket_versions',
        'executed_trades', 'trade_reports', 'cache_status',
        'default_time_range', 'bucket_resampled_datasets',
        'bucket_specs', 'data_loading'
    ]

    # Clear dynamic cache keys (available dates, bucket resampled data, ndx data, vix data)
    keys_to_remove = []
    for key in st.session_state.keys():
        if (key.startswith('available_dates_') or
            key.startswith('bucket_resampled_') or
            key.startswith('ndx_resampled_') or
            key.startswith('ndx_raw_') or
            key.startswith('vix_raw_')):
            keys_to_remove.append(key)

    for key in keys_to_remove:
        del st.session_state[key]

    for key in keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]

def get_cached_backtest_latest_saved():
    """Get backtest_latest_saved data from session state cache"""
    if 'backtest_latest_saved' not in st.session_state:
        st.session_state.backtest_latest_saved = call_api("/data/backtest-latest-saved")
    return st.session_state.backtest_latest_saved