# JerryVision Unified Application

This folder contains the unified version of JerryVision, combining the Streamlit frontend and FastAPI backend into a single Python application.

## How to Run

1. Install dependencies:

```bash
pip install -r requirements.txt
```

2. Start the application:

```bash
streamlit run app.py
```

## What is JerryV<PERSON>?

JerryVision is an analysis and backtesting application for market data. It provides:
- Data refresh and caching
- Visualization of raw and resampled market data
- Executed trades analysis
- Bucket versioning and custom aggregation
- Interactive dashboards and insights

All backend data processing and frontend visualization are now combined in a single app for simplicity. 