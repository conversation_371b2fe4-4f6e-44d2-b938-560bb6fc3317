#!/usr/bin/env python3
"""
Simple test to verify VIX caching imports and basic structure
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all VIX-related imports work"""
    print("Testing imports...")
    
    try:
        # Test core imports
        from core.config import settings
        print("✅ core.config imported")
        
        # Check if VIX file constants exist
        assert hasattr(settings, 'VIX_DATA_FILE'), "VIX_DATA_FILE not found in settings"
        assert hasattr(settings, 'VIX_RESAMPLED_DATA_FILE'), "VIX_RESAMPLED_DATA_FILE not found in settings"
        print("✅ VIX file constants exist in settings")
        
        # Test data processor imports
        from core.data_processor import DataProcessor
        print("✅ DataProcessor imported")
        
        # Check if VIX methods exist
        processor = DataProcessor()
        assert hasattr(processor, 'load_vix_data_from_db'), "load_vix_data_from_db method not found"
        assert hasattr(processor, 'load_vix_data_from_cache'), "load_vix_data_from_cache method not found"
        assert hasattr(processor, 'load_vix_resampled_data_from_cache'), "load_vix_resampled_data_from_cache method not found"
        print("✅ VIX methods exist in DataProcessor")
        
        # Test database imports
        from core.database import MSSQLDatabaseHandler
        print("✅ MSSQLDatabaseHandler imported")
        
        # Check if VIX method exists
        db = MSSQLDatabaseHandler()
        assert hasattr(db, 'get_vix_data'), "get_vix_data method not found"
        print("✅ get_vix_data method exists in MSSQLDatabaseHandler")
        
        # Test cache imports
        from ui.data_cache import get_cached_vix_raw_data, get_cached_vix_resampled_data
        print("✅ VIX cache functions imported")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except AssertionError as e:
        print(f"❌ Assertion error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_file_paths():
    """Test that VIX file paths are correctly configured"""
    print("\nTesting VIX file paths...")
    
    try:
        from core.config import settings
        from core.data_processor import DataProcessor
        
        processor = DataProcessor()
        
        # Check if VIX file paths are set
        assert hasattr(processor, 'vix_data_file'), "vix_data_file not found in processor"
        assert hasattr(processor, 'vix_resampled_data_file'), "vix_resampled_data_file not found in processor"
        
        print(f"✅ VIX data file path: {processor.vix_data_file}")
        print(f"✅ VIX resampled file path: {processor.vix_resampled_data_file}")
        
        # Check if cache variables exist
        assert hasattr(processor, '_cached_vix_data'), "_cached_vix_data not found in processor"
        assert hasattr(processor, '_cached_vix_resampled_data'), "_cached_vix_resampled_data not found in processor"
        print("✅ VIX cache variables exist")
        
        return True
        
    except Exception as e:
        print(f"❌ File path test error: {e}")
        return False

def test_method_signatures():
    """Test that VIX methods have correct signatures"""
    print("\nTesting method signatures...")
    
    try:
        from core.data_processor import DataProcessor
        import inspect
        
        processor = DataProcessor()
        
        # Test load_vix_data_from_cache signature
        sig = inspect.signature(processor.load_vix_data_from_cache)
        params = list(sig.parameters.keys())
        expected_params = ['start_date', 'end_date', 'columns']
        for param in expected_params:
            assert param in params, f"Parameter {param} not found in load_vix_data_from_cache"
        print("✅ load_vix_data_from_cache has correct signature")
        
        # Test load_vix_resampled_data_from_cache signature
        sig = inspect.signature(processor.load_vix_resampled_data_from_cache)
        params = list(sig.parameters.keys())
        for param in expected_params:
            assert param in params, f"Parameter {param} not found in load_vix_resampled_data_from_cache"
        print("✅ load_vix_resampled_data_from_cache has correct signature")
        
        return True
        
    except Exception as e:
        print(f"❌ Method signature test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing VIX Caching Implementation Structure")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_file_paths,
        test_method_signatures
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All structure tests passed! VIX caching implementation structure looks good.")
        print("💡 Note: Database connectivity and actual data tests require a running database.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
