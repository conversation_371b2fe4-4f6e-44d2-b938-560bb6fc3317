#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to connect to the database and retrieve the last 5 rows from the VIX table
"""

import pandas as pd
from core.database import MSSQLDatabaseHandler
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_last_5_vix_rows():
    """Connect to database and get the last 5 rows from VIX table"""
    try:
        # Initialize database handler
        db = MSSQLDatabaseHandler()
        
        # Connect to database
        print("Connecting to database...")
        db.connect()
        print("✅ Successfully connected to database")
        
        # Get VIX data using the existing method
        print("Retrieving VIX data...")
        vix_df = db.get_vix_data()
        
        if vix_df.empty:
            print("❌ No VIX data found in the database")
            return None
            
        print(f"✅ Retrieved {len(vix_df)} rows from VIX table")
        
        # Get the last 5 rows (data is already ordered by date)
        last_5_rows = vix_df.tail(5)
        
        print("\n" + "="*80)
        print("LAST 5 ROWS FROM VIX TABLE:")
        print("="*80)
        
        # Display the data in a nice format
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', None)
        
        print(last_5_rows.to_string())
        
        print("\n" + "="*80)
        print("SUMMARY:")
        print(f"Total VIX records: {len(vix_df)}")
        print(f"Date range: {vix_df.index.min()} to {vix_df.index.max()}")
        print(f"Columns: {list(vix_df.columns)}")
        print("="*80)
        
        # Close database connection
        db.close()
        print("✅ Database connection closed")
        
        return last_5_rows
        
    except Exception as e:
        logger.error(f"Error retrieving VIX data: {str(e)}")
        print(f"❌ Error: {str(e)}")
        return None

if __name__ == "__main__":
    result = get_last_5_vix_rows()
    if result is not None:
        print("\n✅ Successfully retrieved last 5 VIX rows")
    else:
        print("\n❌ Failed to retrieve VIX data")
