#!/usr/bin/env python3
"""
Test script to verify VIX caching implementation
"""

import sys
import os
import pandas as pd
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.data_processor import DataProcessor
from core.database import MSSQLDatabaseHandler
from ui.data_cache import get_cached_vix_raw_data, get_cached_vix_resampled_data
from ui.api_client import call_api

def test_database_connection():
    """Test if we can connect to the database and check for VIX table"""
    print("Testing database connection and VIX table...")
    try:
        db = MSSQLDatabaseHandler()
        db.connect()
        
        # Try to get VIX data
        vix_df = db.get_vix_data()
        print(f"✅ Successfully connected to database")
        print(f"✅ VIX table exists with {len(vix_df)} rows")
        if not vix_df.empty:
            print(f"   Columns: {list(vix_df.columns)}")
            print(f"   Date range: {vix_df.index.min()} to {vix_df.index.max()}")
        return True
    except Exception as e:
        print(f"❌ Database connection or VIX table error: {str(e)}")
        return False

def test_data_processor():
    """Test VIX data loading through data processor"""
    print("\nTesting data processor VIX methods...")
    try:
        processor = DataProcessor()
        
        # Test loading VIX data from database
        vix_df = processor.load_vix_data_from_db()
        print(f"✅ load_vix_data_from_db: {len(vix_df)} rows")
        
        # Test loading VIX data from cache (will be empty initially)
        vix_cache_df = processor.load_vix_data_from_cache()
        print(f"✅ load_vix_data_from_cache: {len(vix_cache_df)} rows")
        
        # Test loading VIX resampled data from cache (will be empty initially)
        vix_resampled_df = processor.load_vix_resampled_data_from_cache()
        print(f"✅ load_vix_resampled_data_from_cache: {len(vix_resampled_df)} rows")
        
        return True
    except Exception as e:
        print(f"❌ Data processor error: {str(e)}")
        return False

def test_api_endpoints():
    """Test VIX API endpoints"""
    print("\nTesting VIX API endpoints...")
    try:
        # Test VIX raw data endpoint
        vix_raw = call_api("/data/vix?limit=100")
        if vix_raw:
            print(f"✅ /data/vix endpoint: {vix_raw.get('rows', 0)} rows")
        else:
            print("⚠️  /data/vix endpoint returned no data (may be expected if no cache)")
        
        # Test VIX resampled data endpoint
        vix_resampled = call_api("/data/vix-resampled?limit=100")
        if vix_resampled:
            print(f"✅ /data/vix-resampled endpoint: {vix_resampled.get('rows', 0)} rows")
        else:
            print("⚠️  /data/vix-resampled endpoint returned no data (may be expected if no cache)")
        
        return True
    except Exception as e:
        print(f"❌ API endpoint error: {str(e)}")
        return False

def test_cache_functions():
    """Test VIX cache functions"""
    print("\nTesting VIX cache functions...")
    try:
        # Note: These will likely return None/empty initially since we haven't refreshed data
        # But we're testing that the functions exist and don't crash
        
        vix_raw_cached = get_cached_vix_raw_data(limit=100)
        print(f"✅ get_cached_vix_raw_data function exists")
        
        vix_resampled_cached = get_cached_vix_resampled_data(limit=100)
        print(f"✅ get_cached_vix_resampled_data function exists")
        
        return True
    except Exception as e:
        print(f"❌ Cache function error: {str(e)}")
        return False

def test_refresh_data():
    """Test data refresh with VIX data"""
    print("\nTesting data refresh with VIX data...")
    try:
        processor = DataProcessor()
        result = processor.refresh_data()
        
        if result and result.get('success'):
            print(f"✅ Data refresh successful")
            print(f"   VIX rows: {result.get('vix_rows', 0)}")
            print(f"   VIX resampled rows: {result.get('vix_resampled_rows', 0)}")
            return True
        else:
            print(f"❌ Data refresh failed: {result.get('message', 'Unknown error')}")
            return False
    except Exception as e:
        print(f"❌ Data refresh error: {str(e)}")
        return False

def main():
    """Run all VIX caching tests"""
    print("🧪 Testing VIX Caching Implementation")
    print("=" * 50)
    
    tests = [
        test_database_connection,
        test_data_processor,
        test_api_endpoints,
        test_cache_functions,
        test_refresh_data
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! VIX caching implementation looks good.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
