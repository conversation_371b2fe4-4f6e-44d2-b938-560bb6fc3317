import pandas as pd
import numpy as np

from core.database import MSSQLDatabaseHandler


db = MSSQLDatabaseHandler()

db.connect()
df = db.get_spx_with_volume_data()

df.reset_index(drop=False, inplace=True)
df['date'] = pd.to_datetime(df['date'])
df['date_only'] = df['date'].dt.date
df['time_only'] = df['date'].dt.time
df.drop(columns=['date'], inplace=True)
df.rename(columns={'date_only': 'date'}, inplace=True)
df.rename(columns={'time_only': 'time'}, inplace=True)

df['DOW'] = [x.weekday() for x in df['date']]

df.dropna(inplace=True)

# for comparison, convert time to string
df['time'] = df['time'].astype(str)

df1 = df[df['time'].isin(['15:49:50','15:50:00', '15:50:10', '15:50:20', '15:50:30'])]
df2 = df[df['time'].isin(['15:50:20','15:50:30', '15:50:40', '15:50:50', '15:51:00'])]
df3 = df[df['time'].isin(['15:59:50'])]

# Find volume spikes (local peaks) within each date
df1_spikes = pd.DataFrame()

for date in df1['date'].unique():
    date_data = df1[df1['date'] == date].copy()
    if len(date_data) > 2:  # Need at least 3 rows for peak detection
        # Find local peaks within this date
        date_data['is_peak'] = (date_data['volume'] > date_data['volume'].shift(1)) & (date_data['volume'] > date_data['volume'].shift(-1))
        peaks = date_data[date_data['is_peak']]
        if not peaks.empty:
            df1_spikes = pd.concat([df1_spikes, peaks])

# If there are multiple spikes on the same date, keep the highest volume one
if not df1_spikes.empty:
    df1_spikes = df1_spikes.loc[df1_spikes.groupby('date')['volume'].idxmax()]


# Find volume spikes (local peaks) within each date
df2_spikes = pd.DataFrame()

for date in df2['date'].unique():
    date_data = df2[df2['date'] == date].copy()
    if len(date_data) > 2:  # Need at least 3 rows for peak detection
        # Find local peaks within this date
        date_data['is_peak'] = (date_data['volume'] > date_data['volume'].shift(1)) & (date_data['volume'] > date_data['volume'].shift(-1))
        peaks = date_data[date_data['is_peak']]
        if not peaks.empty:
            df2_spikes = pd.concat([df2_spikes, peaks])

# If there are multiple spikes on the same date, keep the highest volume one
if not df2_spikes.empty:
    df2_spikes = df2_spikes.loc[df2_spikes.groupby('date')['volume'].idxmax()]



df1 = df1_spikes.merge(df3, on='date', how='left')[['date', 'open_x', 'close_x', 'close_y']]
df2 = df2_spikes.merge(df3, on='date', how='left')[['date', 'open_x', 'close_x', 'close_y']]

df1['move'] = df1['close_x'] - df1['open_x']
df1['profit'] = df1['close_y'] - df1['close_x']
df1['result'] = np.where(np.sign(df1['move']) == np.sign(df1['profit']), 'W', 'L')

df2['move'] = df2['close_x'] - df2['open_x']
df2['profit'] = df2['close_y'] - df2['close_x']
df2['result'] = np.where(np.sign(df2['move']) == np.sign(df2['profit']), 'W', 'L')

print('df1')
print(f'W count: {df1[df1["result"] == "W"]["profit"].abs().count()}')
print(f'W sum: {df1[df1["result"] == "W"]["profit"].abs().sum()}')
print(f'L count: {df1[df1["result"] == "L"]["profit"].abs().count()}')
print(f'L sum: {df1[df1["result"] == "L"]["profit"].abs().sum()}')

print('df2')
print(f'W count: {df2[df2["result"] == "W"]["profit"].abs().count()}')
print(f'W sum: {df2[df2["result"] == "W"]["profit"].abs().sum()}')
print(f'L count: {df2[df2["result"] == "L"]["profit"].abs().count()}')
print(f'L sum: {df2[df2["result"] == "L"]["profit"].abs().sum()}')

breakpoint()