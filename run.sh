#!/bin/bash

# JerryVision Unified Application Runner
echo "🚀 Starting JerryVision Unified Application..."

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source .venv/bin/activate

# Install dependencies
echo "📥 Installing dependencies..."
pip install -r requirements.txt

# Start the application with correct Python path
echo "🎯 Starting Streamlit application..."
PYTHONPATH=. streamlit run app.py --server.port 8501 --server.address 0.0.0.0 