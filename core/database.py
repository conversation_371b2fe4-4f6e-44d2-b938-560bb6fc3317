import os
import pyodbc
import datetime as dt
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from dotenv import load_dotenv, find_dotenv
from .config import settings

logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv(find_dotenv())

# Global database instance for lazy loading
_db_instance = None

def get_db():
    """Get or create database instance (lazy loading)"""
    global _db_instance
    if _db_instance is None:
        _db_instance = MSSQLDatabaseHandler()
    return _db_instance

class MSSQLDatabaseHandler:
    def __init__(self):
        """Initialize connection to SQL Server using environment variables"""
        self.db_ip = settings.DB_IP
        self.db_user = settings.DB_USER
        self.db_password = settings.DB_PASSWORD
        self.db_name = settings.DB_NAME
        
        self.conn = None
        self.cursor = None
        # Don't connect immediately - use lazy initialization
        logger.info(f"MSSQL Database handler initialized for {self.db_ip}")
        
    def _ensure_connection(self):
        """Ensure database connection is established"""
        if self.conn is None:
            self.connect()
        
    def connect(self):
        """Create connection to SQL Server"""
        try:
            conn_str = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.db_ip};"
                f"DATABASE={self.db_name};"
                f"UID={self.db_user};"
                f"PWD={self.db_password};"
                f"TrustServerCertificate=yes;"
            )
            self.conn = pyodbc.connect(conn_str)
            self.cursor = self.conn.cursor()
            logger.info(f"Successfully connected to SQL Server at {self.db_ip}")
        except Exception as e:
            logger.error(f"Error connecting to SQL Server: {str(e)}")
            raise

    def test_connection(self, table_name:str='executed_trades'):
        """Test the connection to the database"""
        try:
            self._ensure_connection()
            sql = f"SELECT count(*) FROM {table_name}"
            self.cursor.execute(sql)
            result = self.cursor.fetchone()
            logger.info(f"Connection test successful: {result[0]} rows found")
            return True
        except Exception as e:
            logger.error(f"Error testing connection: {str(e)}")
            return False

    def ensure_bucket_tables_exist(self):
        """Ensure bucket_version and bucket_spec tables exist"""
        try:
            self._ensure_connection()
            
            # Check if bucket_version table exists
            check_version_sql = """
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='bucket_version' AND xtype='U')
            BEGIN
                CREATE TABLE bucket_version (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    version NVARCHAR(255) NOT NULL,
                    created_at DATETIME2 DEFAULT GETDATE(),
                    updated_at DATETIME2 DEFAULT GETDATE()
                )
            END
            """
            self.cursor.execute(check_version_sql)
            
            # Check if bucket_spec table exists
            check_spec_sql = """
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='bucket_spec' AND xtype='U')
            BEGIN
                CREATE TABLE bucket_spec (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    bucket_start NVARCHAR(10) NOT NULL,
                    bucket_version_id INT NOT NULL,
                    created_at DATETIME2 DEFAULT GETDATE(),
                    updated_at DATETIME2 DEFAULT GETDATE(),
                    FOREIGN KEY (bucket_version_id) REFERENCES bucket_version(id) ON DELETE CASCADE
                )
            END
            """
            self.cursor.execute(check_spec_sql)
            
            self.conn.commit()
            logger.info("Ensured bucket tables exist")
            return True
            
        except Exception as e:
            logger.error(f"Error ensuring bucket tables exist: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def initialize_bucket_data(self):
        """Initialize bucket tables with sample data if they are empty"""
        try:
            self._ensure_connection()
            
            # Ensure tables exist first
            if not self.ensure_bucket_tables_exist():
                return False
            
            # Check if bucket_version table is empty
            self.cursor.execute("SELECT COUNT(*) FROM bucket_version")
            version_count = self.cursor.fetchone()[0]
            
            if version_count == 0:
                # Insert sample bucket versions
                insert_version_sql = "INSERT INTO bucket_version (version) VALUES (?)"
                sample_versions = ['Default', 'Custom_1']
                
                for version in sample_versions:
                    self.cursor.execute(insert_version_sql, (version,))
                
                logger.info("Inserted sample bucket versions")
            
            # Check if bucket_spec table is empty
            self.cursor.execute("SELECT COUNT(*) FROM bucket_spec")
            spec_count = self.cursor.fetchone()[0]
            
            if spec_count == 0:
                # Insert sample bucket specs for Default version (id=1)
                insert_spec_sql = "INSERT INTO bucket_spec (bucket_start, bucket_version_id) VALUES (?, ?)"
                default_specs = ['15:50:00', '15:50:30', '15:51:00', '15:51:30', '15:52:00']
                
                for spec in default_specs:
                    self.cursor.execute(insert_spec_sql, (spec, 1))
                
                # Insert sample bucket specs for Custom_1 version (id=2)
                custom_specs = ['15:45:00', '15:45:30', '15:46:00', '15:46:30', '15:47:00']
                
                for spec in custom_specs:
                    self.cursor.execute(insert_spec_sql, (spec, 2))
                
                logger.info("Inserted sample bucket specs")
            
            self.conn.commit()
            logger.info("Initialized bucket data successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing bucket data: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def get_data(self, table_name):
        """Get all data from a table"""
        try:
            self._ensure_connection()
            sql = f"SELECT * FROM {table_name}"
            self.cursor.execute(sql)
            # Get column names from cursor description
            columns = [column[0] for column in self.cursor.description]
            result = self.cursor.fetchall()
            # Convert the result to a list of lists and create DataFrame
            data = [list(row) for row in result]
            return pd.DataFrame(data, columns=columns)
        except Exception as e:
            logger.error(f"Error getting data from {table_name}: {str(e)}")
            return pd.DataFrame()

    def get_executed_trades_data(self):
        """Get data from the executed_trades table"""
        try:
            self._ensure_connection()

            # First, let's check if the table exists and get its structure
            try:
                sql_check = "SELECT TOP 1 * FROM executed_trades"
                self.cursor.execute(sql_check)
                columns = [column[0] for column in self.cursor.description]
                logger.info(f"Executed trades table columns: {columns}")
            except Exception as e:
                logger.error(f"Error checking executed_trades table structure: {str(e)}")
                logger.info("Table might not exist or have different structure")
                return pd.DataFrame()

            # Build the SQL query
            sql = "SELECT * FROM executed_trades ORDER BY id"

            self.cursor.execute(sql)
            result = self.cursor.fetchall()

            if not result:
                logger.warning("No data found in executed_trades table")
                return pd.DataFrame()

            # Convert the result to a list of lists and create DataFrame
            data = [list(row) for row in result]
            df = pd.DataFrame(data, columns=columns)

            logger.info(f"Retrieved {len(df)} rows from executed_trades")
            return df

        except Exception as e:
            logger.error(f"Error getting executed trades data: {str(e)}")
            return pd.DataFrame()

    def get_trade_reports_data(self):
        """Get data from the trade_reports table"""
        try:
            self._ensure_connection()

            # First, let's check if the table exists and get its structure
            try:
                sql_check = "SELECT TOP 1 * FROM trade_reports"
                self.cursor.execute(sql_check)
                columns = [column[0] for column in self.cursor.description]
                logger.info(f"Trade reports table columns: {columns}")
            except Exception as e:
                logger.error(f"Error checking trade_reports table structure: {str(e)}")
                logger.info("Table might not exist or have different structure")
                return pd.DataFrame()

            # Build the SQL query
            sql = "SELECT * FROM trade_reports ORDER BY id"

            self.cursor.execute(sql)
            result = self.cursor.fetchall()

            if not result:
                logger.warning("No data found in trade_reports table")
                return pd.DataFrame()

            # Convert the result to a list of lists and create DataFrame
            data = [list(row) for row in result]
            df = pd.DataFrame(data, columns=columns)

            logger.info(f"Retrieved {len(df)} rows from trade_reports")
            return df

        except Exception as e:
            logger.error(f"Error getting trade reports data: {str(e)}")
            return pd.DataFrame()

    def get_spx_with_volume_data(self):
        """Get data from the v_SPX_with_Volume view"""
        try:
            self._ensure_connection()
            sql = "SELECT * FROM v_SPX_with_Volume ORDER BY date"
            self.cursor.execute(sql)
            # Get column names from cursor description
            columns = [column[0] for column in self.cursor.description]
            result = self.cursor.fetchall()
            # Convert the result to a list of lists and create DataFrame
            data = [list(row) for row in result]
            df = pd.DataFrame(data, columns=columns)
            
            # Convert date column to datetime
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
            
            logger.info(f"Retrieved {len(df)} rows from v_SPX_with_Volume")
            return df
        except Exception as e:
            logger.error(f"Error getting SPX with volume data: {str(e)}")
            return pd.DataFrame()

    def get_ndx_data(self):
        """Get data from the NDX table"""
        try:
            self._ensure_connection()
            sql = "SELECT * FROM NDX ORDER BY date"
            self.cursor.execute(sql)
            # Get column names from cursor description
            columns = [column[0] for column in self.cursor.description]
            result = self.cursor.fetchall()
            # Convert the result to a list of lists and create DataFrame
            data = [list(row) for row in result]
            df = pd.DataFrame(data, columns=columns)

            # Convert date column to datetime
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)

            # Add volume column if it doesn't exist (NDX doesn't have volume data)
            if 'volume' not in df.columns:
                df['volume'] = 0  # Set volume to 0 for NDX data

            logger.info(f"Retrieved {len(df)} rows from NDX")
            return df
        except Exception as e:
            logger.error(f"Error getting NDX data: {str(e)}")
            return pd.DataFrame()

    def get_vix_data(self):
        """Get data from the VIX table"""
        try:
            self._ensure_connection()
            sql = "SELECT * FROM VIX ORDER BY date"
            self.cursor.execute(sql)
            # Get column names from cursor description and normalize to lowercase
            columns = [column[0].lower() for column in self.cursor.description]
            result = self.cursor.fetchall()
            # Convert the result to a list of lists and create DataFrame
            data = [list(row) for row in result]
            df = pd.DataFrame(data, columns=columns)

            # Convert date column to datetime
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)

            # Add volume column if it doesn't exist (VIX doesn't have volume data)
            if 'volume' not in df.columns:
                df['volume'] = 0  # Set volume to 0 for VIX data

            logger.info(f"Retrieved {len(df)} rows from VIX")
            return df
        except Exception as e:
            logger.error(f"Error getting VIX data: {str(e)}")
            return pd.DataFrame()

    def get_bucket_version_data(self):
        """Get data from the bucket_version table"""
        try:
            self._ensure_connection()
            
            # First, let's check if the table exists and get its structure
            try:
                sql_check = "SELECT TOP 1 * FROM bucket_version"
                self.cursor.execute(sql_check)
                columns = [column[0] for column in self.cursor.description]
                logger.info(f"Bucket version table columns: {columns}")
            except Exception as e:
                logger.error(f"Error checking bucket_version table structure: {str(e)}")
                logger.info("Table might not exist or have different structure")
                return pd.DataFrame()
            
            # Build the SQL query - order by id for bucket versions
            sql = "SELECT * FROM bucket_version ORDER BY id"
            
            self.cursor.execute(sql)
            result = self.cursor.fetchall()
            
            if not result:
                logger.warning("No data found in bucket_version table")
                return pd.DataFrame()
            
            # Convert the result to a list of lists and create DataFrame
            data = [list(row) for row in result]
            df = pd.DataFrame(data, columns=columns)
            
            # For bucket_version, keep id as a regular column, don't set as index
            # This allows us to use it for joins with bucket_spec
            
            logger.info(f"Retrieved {len(df)} rows from bucket_version")
            return df
            
        except Exception as e:
            logger.error(f"Error getting bucket version data: {str(e)}")
            return pd.DataFrame()

    def get_bucket_spec_data(self):
        """Get data from the bucket_spec table"""
        try:
            self._ensure_connection()
            
            # First, let's check if the table exists and get its structure
            try:
                sql_check = "SELECT TOP 1 * FROM bucket_spec"
                self.cursor.execute(sql_check)
                columns = [column[0] for column in self.cursor.description]
                logger.info(f"Bucket spec table columns: {columns}")
            except Exception as e:
                logger.error(f"Error checking bucket_spec table structure: {str(e)}")
                logger.info("Table might not exist or have different structure")
                return pd.DataFrame()
            
            # Build the SQL query - order by id for bucket specs
            sql = "SELECT * FROM bucket_spec ORDER BY id"
            
            self.cursor.execute(sql)
            result = self.cursor.fetchall()
            
            if not result:
                logger.warning("No data found in bucket_spec table")
                return pd.DataFrame()
            
            # Convert the result to a list of lists and create DataFrame
            data = [list(row) for row in result]
            df = pd.DataFrame(data, columns=columns)
            
            logger.info(f"Retrieved {len(df)} rows from bucket_spec")
            return df
            
        except Exception as e:
            logger.error(f"Error getting bucket spec data: {str(e)}")
            return pd.DataFrame()

    def create_bucket_spec(self, bucket_start: str, bucket_version_id: int) -> bool:
        """Create a new bucket spec"""
        try:
            self._ensure_connection()
            
            # Log the input parameters
            logger.info(f"Creating bucket spec: start_time={bucket_start}, version_id={bucket_version_id}")
            
            # Ensure bucket tables exist
            if not self.ensure_bucket_tables_exist():
                logger.error("Failed to ensure bucket tables exist")
                return False
            
            # First check if the bucket_version_id exists
            check_version_sql = "SELECT id FROM bucket_version WHERE id = ?"
            self.cursor.execute(check_version_sql, (bucket_version_id,))
            version_exists = self.cursor.fetchone()
            
            if not version_exists:
                logger.error(f"Bucket version with id {bucket_version_id} does not exist")
                return False
            
            # Check if bucket_spec table exists and get its structure
            try:
                sql_check = "SELECT TOP 1 * FROM bucket_spec"
                self.cursor.execute(sql_check)
                columns = [column[0] for column in self.cursor.description]
                logger.info(f"Bucket spec table columns: {columns}")
            except Exception as e:
                logger.error(f"Error checking bucket_spec table structure: {str(e)}")
                logger.error("Table might not exist or have different structure")
                return False
            
            sql = """
            INSERT INTO bucket_spec (bucket_start, bucket_version_id)
            VALUES (?, ?)
            """
            
            logger.info(f"Executing SQL: {sql} with params: ({bucket_start}, {bucket_version_id})")
            self.cursor.execute(sql, (bucket_start, bucket_version_id))
            self.conn.commit()
            
            logger.info(f"Successfully created bucket spec: {bucket_start} for version {bucket_version_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating bucket spec: {str(e)}")
            logger.error(f"Error type: {type(e).__name__}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            if self.conn:
                self.conn.rollback()
            return False

    def update_bucket_spec(self, bucket_spec_id: int, bucket_start: str) -> bool:
        """Update an existing bucket spec"""
        try:
            self._ensure_connection()
            
            sql = """
            UPDATE bucket_spec 
            SET bucket_start = ?
            WHERE id = ?
            """
            
            self.cursor.execute(sql, (bucket_start, bucket_spec_id))
            self.conn.commit()
            
            if self.cursor.rowcount > 0:
                logger.info(f"Successfully updated bucket spec {bucket_spec_id} to {bucket_start}")
                return True
            else:
                logger.warning(f"No bucket spec found with id {bucket_spec_id}")
                return False
            
        except Exception as e:
            logger.error(f"Error updating bucket spec: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def delete_bucket_spec(self, bucket_spec_id: int) -> bool:
        """Delete a bucket spec"""
        try:
            self._ensure_connection()
            
            sql = "DELETE FROM bucket_spec WHERE id = ?"
            
            self.cursor.execute(sql, (bucket_spec_id,))
            self.conn.commit()
            
            if self.cursor.rowcount > 0:
                logger.info(f"Successfully deleted bucket spec {bucket_spec_id}")
                return True
            else:
                logger.warning(f"No bucket spec found with id {bucket_spec_id}")
                return False
            
        except Exception as e:
            logger.error(f"Error deleting bucket spec: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def create_bucket_version(self, version_name: str) -> bool:
        """Create a new bucket version"""
        try:
            self._ensure_connection()
            
            # Ensure bucket tables exist
            if not self.ensure_bucket_tables_exist():
                logger.error("Failed to ensure bucket tables exist")
                return False
            
            sql = "INSERT INTO bucket_version (version) VALUES (?)"
            
            self.cursor.execute(sql, (version_name,))
            self.conn.commit()
            
            logger.info(f"Successfully created bucket version: {version_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating bucket version: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def update_bucket_version(self, bucket_version_id: int, version_name: str) -> bool:
        """Update an existing bucket version"""
        try:
            self._ensure_connection()
            
            sql = "UPDATE bucket_version SET version = ? WHERE id = ?"
            
            self.cursor.execute(sql, (version_name, bucket_version_id))
            self.conn.commit()
            
            if self.cursor.rowcount > 0:
                logger.info(f"Successfully updated bucket version {bucket_version_id} to {version_name}")
                return True
            else:
                logger.warning(f"No bucket version found with id {bucket_version_id}")
                return False
            
        except Exception as e:
            logger.error(f"Error updating bucket version: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def delete_bucket_version(self, bucket_version_id: int) -> bool:
        """Delete a bucket version and its associated specs"""
        try:
            self._ensure_connection()
            
            # First delete associated bucket specs
            delete_specs_sql = "DELETE FROM bucket_spec WHERE bucket_version_id = ?"
            self.cursor.execute(delete_specs_sql, (bucket_version_id,))
            
            # Then delete the bucket version
            delete_version_sql = "DELETE FROM bucket_version WHERE id = ?"
            self.cursor.execute(delete_version_sql, (bucket_version_id,))
            
            self.conn.commit()
            
            logger.info(f"Successfully deleted bucket version {bucket_version_id} and its specs")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting bucket version: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def close(self):
        """Close database connection"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
            logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing database connection: {str(e)}")

    def get_backtest_latest_saved_data(self):
        """Get data from the backtest_latest_saved table"""
        try:
            self._ensure_connection()

            # First, let's check if the table exists and get its structure
            try:
                sql_check = "SELECT TOP 1 * FROM backtest_latest_saved"
                self.cursor.execute(sql_check)
                columns = [column[0] for column in self.cursor.description]
                logger.info(f"backtest_latest_saved table columns: {columns}")
            except Exception as e:
                logger.error(f"Error checking backtest_latest_saved table structure: {str(e)}")
                logger.info("Table might not exist or have different structure")
                return pd.DataFrame()

            # Build the SQL query
            sql = "SELECT * FROM backtest_latest_saved ORDER BY run_id"

            self.cursor.execute(sql)
            result = self.cursor.fetchall()

            if not result:
                logger.warning("No data found in backtest_latest_saved table")
                return pd.DataFrame()

            # Convert the result to a list of lists and create DataFrame
            data = [list(row) for row in result]
            df = pd.DataFrame(data, columns=columns)

            logger.info(f"Retrieved {len(df)} rows from backtest_latest_saved")
            return df

        except Exception as e:
            logger.error(f"Error getting backtest_latest_saved data: {str(e)}")
            return pd.DataFrame()

# For backward compatibility, provide a db instance
# But use lazy loading to avoid circular imports
def get_db_instance():
    """Get database instance (backward compatibility)"""
    return get_db()

# Create a db instance for backward compatibility, but don't initialize it immediately
db = None
def _get_db():
    global db
    if db is None:
        db = get_db()
    return db 