import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, date
from pathlib import Path
from typing import Dict, Tuple, Optional, Union, List
import pyarrow.parquet as pq
import pyarrow as pa
from core.config import settings

logger = logging.getLogger(__name__)

class DataProcessor:
    def __init__(self):
        self.cache_dir = settings.CACHE_DIR
        self.raw_data_file = self.cache_dir / settings.RAW_DATA_FILE
        self.ndx_data_file = self.cache_dir / settings.NDX_DATA_FILE
        self.vix_data_file = self.cache_dir / settings.VIX_DATA_FILE
        self.executed_trades_file = self.cache_dir / settings.EXECUTED_TRADES_FILE
        self.trade_reports_file = self.cache_dir / settings.TRADE_REPORTS_FILE
        self.backtest_latest_saved_file = self.cache_dir / settings.BACKTEST_LATEST_SAVED_FILE
        self.resampled_data_file = self.cache_dir / settings.RESAMPLED_DATA_FILE
        self.ndx_resampled_data_file = self.cache_dir / settings.NDX_RESAMPLED_DATA_FILE
        self.bucket_version_file = self.cache_dir / settings.BUCKET_VERSION_FILE
        self.bucket_spec_file = self.cache_dir / settings.BUCKET_SPEC_FILE

        self.bucket_resampled_dir = self.cache_dir / settings.BUCKET_RESAMPLED_DIR
        self.cache_status_file = self.cache_dir / settings.CACHE_STATUS_FILE
        self.resample_freq = settings.RESAMPLE_FREQUENCY

        # Instance-level cache
        self._cached_data = None
        self._cached_bucket_resampled = None
        self._cached_ndx_data = None
        self._cached_ndx_resampled_data = None
        self._cached_vix_data = None
        
    def _get_db(self):
        """Get database instance (lazy loading to avoid circular imports)"""
        from core.database import get_db
        return get_db()
        
    def load_raw_data_from_db(self) -> pd.DataFrame:
        """Load raw data from SQL Server view"""
        try:
            logger.info("Loading raw data from v_SPX_with_Volume view...")
            db = self._get_db()
            df = db.get_spx_with_volume_data()
            
            if df.empty:
                logger.warning("No data retrieved from database")
                return df
                
            # Ensure we have the required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"Missing required columns: {missing_columns}")
                return pd.DataFrame()
            
            # Filter for market hours (9:00 AM to 4:00 PM)
            market_open = settings.MARKET_OPEN_TIME
            market_close = settings.MARKET_CLOSE_TIME
            logger.info(f"Filtering data for market hours ({market_open} - {market_close})...")
            df_filtered = df.between_time(market_open, market_close)
            
            logger.info(f"Filtered from {len(df)} to {len(df_filtered)} rows (market hours only)")
                
            logger.info(f"Successfully loaded {len(df_filtered)} rows of raw data")
            return df_filtered
            
        except Exception as e:
            logger.error(f"Error loading raw data from database: {str(e)}")
            return pd.DataFrame()

    def load_ndx_data_from_db(self) -> pd.DataFrame:
        """Load NDX data from SQL Server table"""
        try:
            logger.info("Loading NDX data from NDX table...")
            db = self._get_db()
            df = db.get_ndx_data()

            if df.empty:
                logger.warning("No NDX data retrieved from database")
                return df

            # Ensure we have the required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                logger.error(f"Missing required columns: {missing_columns}")
                return pd.DataFrame()

            # Filter for market hours (9:00 AM to 4:00 PM)
            market_open = settings.MARKET_OPEN_TIME
            market_close = settings.MARKET_CLOSE_TIME
            logger.info(f"Filtering NDX data for market hours ({market_open} - {market_close})...")
            df_filtered = df.between_time(market_open, market_close)

            logger.info(f"Filtered NDX from {len(df)} to {len(df_filtered)} rows (market hours only)")

            logger.info(f"Successfully loaded {len(df_filtered)} rows of NDX data")
            return df_filtered

        except Exception as e:
            logger.error(f"Error loading NDX data from database: {str(e)}")
            return pd.DataFrame()

    def load_vix_data_from_db(self) -> pd.DataFrame:
        """Load VIX data from SQL Server table"""
        try:
            logger.info("Loading VIX data from VIX table...")
            db = self._get_db()
            df = db.get_vix_data()

            if df.empty:
                logger.warning("No VIX data retrieved from database")
                return df

            # Ensure we have the required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                logger.error(f"Missing required columns: {missing_columns}")
                return pd.DataFrame()

            # VIX data is daily data (not intraday), so no market hours filtering needed
            # Convert numeric columns to float for consistency
            numeric_columns = ['open', 'high', 'low', 'close']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            logger.info(f"Successfully loaded {len(df)} rows of VIX data")
            return df

        except Exception as e:
            logger.error(f"Error loading VIX data from database: {str(e)}")
            return pd.DataFrame()
    
    def load_executed_trades_from_db(self) -> pd.DataFrame:
        """Load executed trades data from SQL Server table"""
        try:
            logger.info("Loading executed trades data from executed_trades table...")
            db = self._get_db()
            df = db.get_executed_trades_data()

            if df.empty:
                logger.warning("No executed trades data retrieved from database")
                return df

            logger.info(f"Successfully loaded {len(df)} rows of executed trades data")
            return df

        except Exception as e:
            logger.error(f"Error loading executed trades data from database: {str(e)}")
            return pd.DataFrame()

    def load_trade_reports_from_db(self) -> pd.DataFrame:
        """Load trade reports data from SQL Server table"""
        try:
            logger.info("Loading trade reports data from trade_reports table...")
            db = self._get_db()
            df = db.get_trade_reports_data()

            if df.empty:
                logger.warning("No trade reports data retrieved from database")
                return df

            logger.info(f"Successfully loaded {len(df)} rows of trade reports data")
            return df

        except Exception as e:
            logger.error(f"Error loading trade reports data from database: {str(e)}")
            return pd.DataFrame()
    
    def load_bucket_version_from_db(self) -> pd.DataFrame:
        """Load bucket version data from SQL Server table"""
        try:
            logger.info("Loading bucket version data from bucket_version table...")
            db = self._get_db()
            
            # Initialize bucket data if needed
            db.initialize_bucket_data()
            
            df = db.get_bucket_version_data()
            
            if df.empty:
                logger.warning("No bucket version data retrieved from database")
                return df
                
            logger.info(f"Successfully loaded {len(df)} rows of bucket version data")
            return df
            
        except Exception as e:
            logger.error(f"Error loading bucket version data from database: {str(e)}")
            return pd.DataFrame()
    
    def load_bucket_spec_from_db(self) -> pd.DataFrame:
        """Load bucket spec data from SQL Server table"""
        try:
            logger.info("Loading bucket spec data from bucket_spec table...")
            db = self._get_db()
            
            # Initialize bucket data if needed
            db.initialize_bucket_data()
            
            df = db.get_bucket_spec_data()
            
            if df.empty:
                logger.warning("No bucket spec data retrieved from database")
                return df
                
            logger.info(f"Successfully loaded {len(df)} rows of bucket spec data")
            return df
            
        except Exception as e:
            logger.error(f"Error loading bucket spec data from database: {str(e)}")
            return pd.DataFrame()
    
    def load_backtest_latest_saved_from_db(self) -> pd.DataFrame:
        """Load backtest_latest_saved data from SQL Server table"""
        try:
            logger.info("Loading backtest_latest_saved data from backtest_latest_saved table...")
            db = self._get_db()
            df = db.get_backtest_latest_saved_data()

            if df.empty:
                logger.warning("No backtest_latest_saved data retrieved from database")
                return df

            logger.info(f"Successfully loaded {len(df)} rows of backtest_latest_saved data")
            return df

        except Exception as e:
            logger.error(f"Error loading backtest_latest_saved data from database: {str(e)}")
            return pd.DataFrame()

    def resample_to_30s(self, df: pd.DataFrame) -> pd.DataFrame:
        """Resample data to 30-second OHLCV buckets"""
        try:
            if df.empty:
                logger.warning("No data to resample")
                return df
                
            logger.info(f"Resampling {len(df)} rows to {self.resample_freq} frequency...")
            
            # Resample with OHLCV aggregation
            resampled = df.resample(self.resample_freq).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            })
            
            # Remove rows with all NaN values (empty buckets)
            resampled = resampled.dropna(how='all')
            
            # Additional filtering: Remove dates that don't have meaningful data
            # Group by date and filter out dates with insufficient data
            resampled_with_date = resampled.copy()
            resampled_with_date['date_only'] = resampled_with_date.index.date
            
            # Filter out dates with less than 10 data points (indicating incomplete trading day)
            date_counts = resampled_with_date.groupby('date_only').size()
            valid_dates = date_counts[date_counts >= 10].index
            
            # Keep only data from valid dates
            date_series = pd.Series(resampled.index.date, index=resampled.index)
            resampled_filtered = resampled[date_series.isin(valid_dates)]
            
            # Additional filtering: Remove any remaining rows with NaN values in key columns
            resampled_filtered = resampled_filtered.dropna(subset=['open', 'high', 'low', 'close'])
            
            logger.info(f"Resampled to {len(resampled)} rows, filtered to {len(resampled_filtered)} rows with meaningful data")
            
            # Verify that 30s data has fewer rows than input 10s data
            if len(resampled_filtered) >= len(df):
                logger.warning(f"30s data ({len(resampled_filtered)}) has more or equal rows than 10s data ({len(df)}) - this indicates a resampling issue")
            
            return resampled_filtered
            
        except Exception as e:
            logger.error(f"Error resampling data: {str(e)}")
            return pd.DataFrame()
    
    def save_to_cache(self, raw_df: pd.DataFrame, resampled_df: pd.DataFrame, bucket_version_df: pd.DataFrame = None, bucket_spec_df: pd.DataFrame = None, executed_trades_df: pd.DataFrame = None, trade_reports_df: pd.DataFrame = None, bucket_resampled_datasets: Dict[str, pd.DataFrame] = None, ndx_df: pd.DataFrame = None, ndx_resampled_df: pd.DataFrame = None, vix_df: pd.DataFrame = None, backtest_latest_saved_df: pd.DataFrame = None) -> bool:
        """Save data to cache files"""
        try:
            logger.info("Saving data to cache...")
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            if not raw_df.empty:
                raw_df.to_parquet(self.raw_data_file)
                logger.info(f"Saved {len(raw_df)} rows of raw data to cache")
            if not ndx_df.empty:
                ndx_df.to_parquet(self.ndx_data_file)
                logger.info(f"Saved {len(ndx_df)} rows of NDX data to cache")
            if vix_df is not None and not vix_df.empty:
                vix_df.to_parquet(self.vix_data_file)
                logger.info(f"Saved {len(vix_df)} rows of VIX data to cache")
            if executed_trades_df is not None and not executed_trades_df.empty:
                executed_trades_df.to_parquet(self.executed_trades_file)
                logger.info(f"Saved {len(executed_trades_df)} rows of executed trades data to cache")
            if trade_reports_df is not None and not trade_reports_df.empty:
                trade_reports_df.to_parquet(self.trade_reports_file)
                logger.info(f"Saved {len(trade_reports_df)} rows of trade reports data to cache")
            if not resampled_df.empty:
                resampled_df.to_parquet(self.resampled_data_file)
                logger.info(f"Saved {len(resampled_df)} rows of resampled data to cache")
            if not ndx_resampled_df.empty:
                ndx_resampled_df.to_parquet(self.ndx_resampled_data_file)
                logger.info(f"Saved {len(ndx_resampled_df)} rows of NDX resampled data to cache")
            if bucket_version_df is not None and not bucket_version_df.empty:
                bucket_version_df.to_parquet(self.bucket_version_file)
                logger.info(f"Saved {len(bucket_version_df)} rows of bucket version data to cache")
            if bucket_spec_df is not None and not bucket_spec_df.empty:
                bucket_spec_df.to_parquet(self.bucket_spec_file)
                logger.info(f"Saved {len(bucket_spec_df)} rows of bucket spec data to cache")
            if backtest_latest_saved_df is not None and not backtest_latest_saved_df.empty:
                backtest_latest_saved_df.to_parquet(self.backtest_latest_saved_file)
                logger.info(f"Saved {len(backtest_latest_saved_df)} rows of backtest_latest_saved data to cache")

            if bucket_resampled_datasets:
                bucket_resampled_dir = self.bucket_resampled_dir
                bucket_resampled_dir.mkdir(parents=True, exist_ok=True)
                for bucket_name, df in bucket_resampled_datasets.items():
                    if not df.empty:
                        file_path = bucket_resampled_dir / f"{bucket_name}.parquet"
                        df.to_parquet(file_path)
                        logger.info(f"Saved {len(df)} rows of bucket resampled data '{bucket_name}' to cache")
            self._update_cache_status()
            logger.info("Successfully saved all data to cache")
            return True
        except Exception as e:
            logger.error(f"Error saving data to cache: {str(e)}")
            return False

    def load_from_cache(self, start_date: Optional[Union[str, date, datetime]] = None,
                       end_date: Optional[Union[str, date, datetime]] = None,
                       columns: Optional[List[str]] = None) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Load data from cache files with optional date filtering and column selection"""
        # Don't use cached data if filters are applied
        if self._cached_data is not None and start_date is None and end_date is None and columns is None:
            logger.info("Using cached data (no disk I/O)")
            return self._cached_data

        try:
            logger.info("Loading data from cache...")

            # Load raw data with filtering
            raw_df = self._load_parquet_with_filters(
                self.raw_data_file, start_date, end_date, columns, "raw data"
            )

            # Load resampled data with filtering
            resampled_df = self._load_parquet_with_filters(
                self.resampled_data_file, start_date, end_date, columns, "resampled data"
            )

            # Load metadata files (no date filtering needed for these)
            bucket_version_df = pd.DataFrame()
            if self.bucket_version_file.exists():
                bucket_version_df = pd.read_parquet(self.bucket_version_file)
                logger.info(f"Loaded {len(bucket_version_df)} rows of bucket version data from cache")
            bucket_spec_df = pd.DataFrame()
            if self.bucket_spec_file.exists():
                bucket_spec_df = pd.read_parquet(self.bucket_spec_file)
                logger.info(f"Loaded {len(bucket_spec_df)} rows of bucket spec data from cache")

            executed_trades_df = pd.DataFrame()
            if self.executed_trades_file.exists():
                executed_trades_df = pd.read_parquet(self.executed_trades_file)
                logger.info(f"Loaded {len(executed_trades_df)} rows of executed trades data from cache")

            trade_reports_df = pd.DataFrame()
            if self.trade_reports_file.exists():
                trade_reports_df = pd.read_parquet(self.trade_reports_file)
                logger.info(f"Loaded {len(trade_reports_df)} rows of trade reports data from cache")

            backtest_latest_saved_df = pd.DataFrame()
            if self.backtest_latest_saved_file.exists():
                backtest_latest_saved_df = pd.read_parquet(self.backtest_latest_saved_file)
                logger.info(f"Loaded {len(backtest_latest_saved_df)} rows of backtest_latest_saved data from cache")

            # Only cache the result if no filters were applied
            if start_date is None and end_date is None and columns is None:
                self._cached_data = (raw_df, resampled_df, bucket_version_df, bucket_spec_df, executed_trades_df, trade_reports_df, backtest_latest_saved_df)

            return (raw_df, resampled_df, bucket_version_df, bucket_spec_df, executed_trades_df, trade_reports_df, backtest_latest_saved_df)
        except Exception as e:
            logger.error(f"Error loading data from cache: {str(e)}")
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
    
    def load_raw_data_from_cache(self, start_date: Optional[Union[str, date, datetime]] = None,
                                end_date: Optional[Union[str, date, datetime]] = None,
                                columns: Optional[List[str]] = None) -> pd.DataFrame:
        """Load raw data from cache with optional date filtering and column selection"""
        # If no filters and we have cached data, use it
        if self._cached_data is not None and start_date is None and end_date is None and columns is None:
            logger.info("Using cached raw data (no disk I/O)")
            return self._cached_data[0]  # raw_df is first in the tuple

        return self._load_parquet_with_filters(self.raw_data_file, start_date, end_date, columns, "raw data")

    def load_resampled_data_from_cache(self, start_date: Optional[Union[str, date, datetime]] = None,
                                      end_date: Optional[Union[str, date, datetime]] = None,
                                      columns: Optional[List[str]] = None) -> pd.DataFrame:
        """Load resampled data from cache with optional date filtering and column selection"""
        # If no filters and we have cached data, use it
        if self._cached_data is not None and start_date is None and end_date is None and columns is None:
            logger.info("Using cached resampled data (no disk I/O)")
            return self._cached_data[1]  # resampled_df is second in the tuple

        return self._load_parquet_with_filters(self.resampled_data_file, start_date, end_date, columns, "resampled data")

    def load_ndx_data_from_cache(self, start_date: Optional[Union[str, date, datetime]] = None,
                                end_date: Optional[Union[str, date, datetime]] = None,
                                columns: Optional[List[str]] = None) -> pd.DataFrame:
        """Load NDX data from cache with optional date filtering and column selection"""
        # If no filters and we have cached data, use it
        if self._cached_ndx_data is not None and start_date is None and end_date is None and columns is None:
            logger.info("Using cached NDX data (no disk I/O)")
            return self._cached_ndx_data

        ndx_df = self._load_parquet_with_filters(self.ndx_data_file, start_date, end_date, columns, "NDX data")

        # Cache if no filters were applied
        if start_date is None and end_date is None and columns is None:
            self._cached_ndx_data = ndx_df

        return ndx_df

    def load_vix_data_from_cache(self, start_date: Optional[Union[str, date, datetime]] = None,
                                end_date: Optional[Union[str, date, datetime]] = None,
                                columns: Optional[List[str]] = None) -> pd.DataFrame:
        """Load VIX data from cache with optional date filtering and column selection"""
        # If no filters and we have cached data, use it
        if self._cached_vix_data is not None and start_date is None and end_date is None and columns is None:
            logger.info("Using cached VIX data (no disk I/O)")
            return self._cached_vix_data

        vix_df = self._load_parquet_with_filters(self.vix_data_file, start_date, end_date, columns, "VIX data")

        # Cache if no filters were applied
        if start_date is None and end_date is None and columns is None:
            self._cached_vix_data = vix_df

        return vix_df

    def load_ndx_resampled_data_from_cache(self, start_date: Optional[Union[str, date, datetime]] = None,
                                          end_date: Optional[Union[str, date, datetime]] = None,
                                          columns: Optional[List[str]] = None) -> pd.DataFrame:
        """Load NDX resampled data from cache with optional date filtering and column selection"""
        # If no filters and we have cached data, use it
        if self._cached_ndx_resampled_data is not None and start_date is None and end_date is None and columns is None:
            logger.info("Using cached NDX resampled data (no disk I/O)")
            return self._cached_ndx_resampled_data

        ndx_resampled_df = self._load_parquet_with_filters(self.ndx_resampled_data_file, start_date, end_date, columns, "NDX resampled data")

        # Cache if no filters were applied
        if start_date is None and end_date is None and columns is None:
            self._cached_ndx_resampled_data = ndx_resampled_df

        return ndx_resampled_df



    def load_bucket_resampled_from_cache(self, bucket_name: Optional[str] = None,
                                        start_date: Optional[Union[str, date, datetime]] = None,
                                        end_date: Optional[Union[str, date, datetime]] = None,
                                        columns: Optional[List[str]] = None) -> Dict[str, pd.DataFrame]:
        """Load bucket resampled datasets from cache with optional filtering"""
        # Use cached data if no date/column filters are applied
        if self._cached_bucket_resampled is not None and start_date is None and end_date is None and columns is None:
            if bucket_name is None:
                # Return all cached datasets
                logger.info("Using cached bucket resampled data (no disk I/O)")
                return self._cached_bucket_resampled
            elif bucket_name in self._cached_bucket_resampled:
                # Return specific bucket from cache
                logger.info(f"Using cached bucket resampled data for '{bucket_name}' (no disk I/O)")
                return {bucket_name: self._cached_bucket_resampled[bucket_name]}
            # If specific bucket not in cache, fall through to load from disk
            
        try:
            bucket_resampled_datasets = {}
            bucket_resampled_dir = self.bucket_resampled_dir  # Already a full path

            if bucket_resampled_dir.exists():
                # Get list of files to process
                parquet_files = list(bucket_resampled_dir.glob("*.parquet"))

                # Filter by bucket name if specified
                if bucket_name:
                    parquet_files = [f for f in parquet_files if f.stem == bucket_name]

                for file_path in parquet_files:
                    current_bucket_name = file_path.stem
                    df = self._load_parquet_with_filters(
                        file_path, start_date, end_date, columns, f"bucket resampled data '{current_bucket_name}'"
                    )
                    if not df.empty:
                        bucket_resampled_datasets[current_bucket_name] = df

            logger.info(f"Successfully loaded {len(bucket_resampled_datasets)} bucket resampled datasets from cache")

            # Cache the result if no date/column filters were applied
            if start_date is None and end_date is None and columns is None:
                if bucket_name is None:
                    # Cache all datasets
                    self._cached_bucket_resampled = bucket_resampled_datasets
                else:
                    # Update cache with specific bucket data
                    if self._cached_bucket_resampled is None:
                        self._cached_bucket_resampled = {}
                    self._cached_bucket_resampled.update(bucket_resampled_datasets)

            return bucket_resampled_datasets
            
        except Exception as e:
            logger.error(f"Error loading bucket resampled data from cache: {str(e)}")
            return {}
    
    def adjust_resampled_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Adjust resampled data to fill gaps between sequential bars"""
        if df.empty:
            return df
        
        # Create a copy to avoid modifying the original
        adjusted_df = df.copy()
        
        # Sort by timestamp to ensure proper order
        adjusted_df = adjusted_df.sort_index()
        
        # Group by date to handle gaps between days
        adjusted_df['date'] = adjusted_df.index.date
        
        # Process each date separately
        for date in adjusted_df['date'].unique():
            daily_data = adjusted_df[adjusted_df['date'] == date]
            
            if len(daily_data) <= 1:
                continue  # Skip if only one bar for the day
            
            # Get the indices for this day's data
            daily_indices = daily_data.index
            
            # Adjust each bar starting from the second bar
            for i in range(1, len(daily_indices)):
                current_idx = daily_indices[i]
                prev_idx = daily_indices[i-1]
                
                # Set open to previous bar's close
                prev_close = adjusted_df.loc[prev_idx, 'close']
                adjusted_df.loc[current_idx, 'open'] = prev_close
                
                # Adjust high and low to account for the new open
                current_high = adjusted_df.loc[current_idx, 'high']
                current_low = adjusted_df.loc[current_idx, 'low']
                
                # High should be max of original high and new open
                adjusted_df.loc[current_idx, 'high'] = max(current_high, prev_close)
                
                # Low should be min of original low and new open
                adjusted_df.loc[current_idx, 'low'] = min(current_low, prev_close)
        
        # Remove the temporary date column
        adjusted_df = adjusted_df.drop('date', axis=1)
        
        return adjusted_df

    def generate_bucket_resampled_data(self, raw_df: pd.DataFrame, bucket_version_df: pd.DataFrame, bucket_spec_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Generate bucket resampled data based on bucket versions and specs"""
        try:
            bucket_resampled_datasets = {}
            
            if raw_df.empty or bucket_version_df.empty or bucket_spec_df.empty:
                logger.warning("Missing data for bucket resampled generation")
                return bucket_resampled_datasets
            
            logger.info(f"Processing {len(bucket_version_df)} bucket versions")
            logger.info(f"Available bucket versions: {bucket_version_df['version'].tolist()}")
            
            # Process each bucket version
            for _, version_row in bucket_version_df.iterrows():
                version_id = version_row['id']
                version_name = version_row['version']
                
                logger.info(f"Processing bucket version: {version_name} (ID: {version_id})")
                
                # Get bucket specs for this version
                version_specs = bucket_spec_df[bucket_spec_df['bucket_version_id'] == version_id]
                
                if version_specs.empty:
                    logger.warning(f"No bucket specs found for version {version_name} (ID: {version_id})")
                    continue
                
                logger.info(f"Found {len(version_specs)} bucket specs for version {version_name}")
                logger.info(f"Bucket start times: {version_specs['bucket_start'].tolist()}")
                
                # Sort bucket specs by start time
                version_specs = version_specs.sort_values('bucket_start')
                
                # Create resampled data for this version
                resampled_data = []
                
                # Group raw data by date
                raw_df_with_date = raw_df.copy()
                raw_df_with_date['date_only'] = raw_df_with_date.index.date
                
                unique_dates = raw_df_with_date['date_only'].unique()
                logger.info(f"Processing {len(unique_dates)} unique dates for version {version_name}")
                
                for date in unique_dates:
                    daily_data = raw_df_with_date[raw_df_with_date['date_only'] == date]
                    
                    # Convert bucket start times to datetime for comparison
                    bucket_start_times = []
                    for _, spec_row in version_specs.iterrows():
                        bucket_start_str = spec_row['bucket_start']
                        try:
                            bucket_start_time = pd.to_datetime(bucket_start_str).time()
                        except:
                            # If direct conversion fails, try parsing the time string
                            bucket_start_time = pd.to_datetime(f"2000-01-01 {bucket_start_str}").time()
                        
                        bucket_start_datetime = pd.Timestamp.combine(date, bucket_start_time)
                        bucket_start_times.append(bucket_start_datetime)
                    
                    # Sort bucket start times
                    bucket_start_times.sort()
                    
                    logger.debug(f"Date {date}: Processing {len(bucket_start_times)} buckets for version {version_name}")
                    
                    # Process each bucket
                    for i, bucket_start_datetime in enumerate(bucket_start_times):
                        # Determine the end time for this bucket
                        if i < len(bucket_start_times) - 1:
                            # Use the next bucket's start time as the end time (exclusive)
                            bucket_end_datetime = bucket_start_times[i + 1]
                        else:
                            # For the last bucket, use the end of the day
                            bucket_end_datetime = pd.Timestamp.combine(date, pd.to_datetime("23:59:59").time())
                        
                        # Get all data from bucket start time up to (but not including) bucket end time
                        bucket_data = daily_data[
                            (daily_data.index >= bucket_start_datetime) & 
                            (daily_data.index < bucket_end_datetime)
                        ]
                        
                        if not bucket_data.empty:
                            # Aggregate OHLCV for this bucket
                            bucket_ohlcv = {
                                'timestamp': bucket_start_datetime,
                                'open': bucket_data['open'].iloc[0] if len(bucket_data) > 0 else None,
                                'high': bucket_data['high'].max() if len(bucket_data) > 0 else None,
                                'low': bucket_data['low'].min() if len(bucket_data) > 0 else None,
                                'close': bucket_data['close'].iloc[-1] if len(bucket_data) > 0 else None,
                                'volume': bucket_data['volume'].sum() if len(bucket_data) > 0 else 0
                            }
                            resampled_data.append(bucket_ohlcv)
                
                if resampled_data:
                    # Create DataFrame for this version
                    version_df = pd.DataFrame(resampled_data)
                    version_df.set_index('timestamp', inplace=True)
                    
                    # Remove rows with NaN values in key columns
                    version_df = version_df.dropna(subset=['open', 'high', 'low', 'close'])
                    
                    if not version_df.empty:
                        # Adjust the resampled data to fill gaps
                        version_df = self.adjust_resampled_data(version_df)
                        
                        # Clean the version name to avoid file naming issues
                        clean_version_name = version_name.strip()
                        bucket_resampled_datasets[clean_version_name] = version_df
                        logger.info(f"Generated {len(version_df)} rows of bucket resampled data for version '{clean_version_name}'")
                        logger.info(f"Time range: {version_df.index.min()} to {version_df.index.max()}")
                    else:
                        logger.warning(f"No valid data generated for version '{version_name}'")
                else:
                    logger.warning(f"No data generated for version '{version_name}'")
            
            logger.info(f"Successfully generated {len(bucket_resampled_datasets)} bucket resampled datasets")
            logger.info(f"Generated datasets: {list(bucket_resampled_datasets.keys())}")
            return bucket_resampled_datasets
            
        except Exception as e:
            logger.error(f"Error generating bucket resampled data: {str(e)}")
            return {}

    def refresh_data(self) -> Dict[str, any]:
        """Refresh all data from database and save to cache"""
        try:
            logger.info("Starting data refresh...")
            
            # Load data from database
            raw_df = self.load_raw_data_from_db()
            ndx_df = self.load_ndx_data_from_db()
            vix_df = self.load_vix_data_from_db()
            executed_trades_df = self.load_executed_trades_from_db()
            trade_reports_df = self.load_trade_reports_from_db()
            bucket_version_df = self.load_bucket_version_from_db()
            bucket_spec_df = self.load_bucket_spec_from_db()
            backtest_latest_saved_df = self.load_backtest_latest_saved_from_db()


            # Resample raw data to 30s
            resampled_df = pd.DataFrame()
            if not raw_df.empty:
                resampled_df = self.resample_to_30s(raw_df)

            # Resample NDX data to 30s
            ndx_resampled_df = pd.DataFrame()
            if not ndx_df.empty:
                ndx_resampled_df = self.resample_to_30s(ndx_df)

            # Adjust resampled data
            resampled_df = self.adjust_resampled_data(resampled_df)
            ndx_resampled_df = self.adjust_resampled_data(ndx_resampled_df)
            
            # Generate bucket resampled data
            bucket_resampled_datasets = {}
            if not raw_df.empty and not bucket_version_df.empty and not bucket_spec_df.empty:
                bucket_resampled_datasets = self.generate_bucket_resampled_data(raw_df, bucket_version_df, bucket_spec_df)
            
            # Save to cache
            success = self.save_to_cache(
                raw_df=raw_df,
                resampled_df=resampled_df,
                bucket_version_df=bucket_version_df,
                bucket_spec_df=bucket_spec_df,
                executed_trades_df=executed_trades_df,
                trade_reports_df=trade_reports_df,
                bucket_resampled_datasets=bucket_resampled_datasets,
                ndx_df=ndx_df,
                ndx_resampled_df=ndx_resampled_df,
                vix_df=vix_df,
                backtest_latest_saved_df=backtest_latest_saved_df
            )
            
            if success:
                # Clear instance cache to force reload on next access
                self._cached_data = None
                self._cached_bucket_resampled = None
                self._cached_ndx_data = None
                self._cached_ndx_resampled_data = None
                self._cached_vix_data = None

                logger.info("Data refresh completed successfully")
                return {
                    "success": True,
                    "message": "Data refreshed successfully",
                    "raw_rows": len(raw_df),
                    "ndx_rows": len(ndx_df),
                    "vix_rows": len(vix_df),
                    "resampled_rows": len(resampled_df),
                    "ndx_resampled_rows": len(ndx_resampled_df),
                    "executed_trades_rows": len(executed_trades_df),
                    "trade_reports_rows": len(trade_reports_df),
                    "bucket_version_rows": len(bucket_version_df),
                    "bucket_spec_rows": len(bucket_spec_df),
                    "bucket_resampled_datasets": len(bucket_resampled_datasets)
                }
            else:
                logger.error("Failed to save data to cache")
                return {
                    "success": False,
                    "message": "Failed to save data to cache"
                }
                
        except Exception as e:
            logger.error(f"Error during data refresh: {str(e)}")
            return {
                "success": False,
                "message": f"Error during data refresh: {str(e)}"
            }
    
    def get_cache_status(self) -> Dict[str, any]:
        """Get cache status information"""
        try:
            status = {
                "raw_data_exists": self.raw_data_file.exists(),
                "resampled_data_exists": self.resampled_data_file.exists(),
                "ndx_data_exists": self.ndx_data_file.exists(),
                "ndx_resampled_data_exists": self.ndx_resampled_data_file.exists(),
                "vix_data_exists": self.vix_data_file.exists(),
                "executed_trades_exists": self.executed_trades_file.exists(),
                "trade_reports_exists": self.trade_reports_file.exists(),
                "bucket_version_exists": self.bucket_version_file.exists(),
                "bucket_spec_exists": self.bucket_spec_file.exists(),
                "raw_data_size": 0,
                "resampled_data_size": 0,
                "ndx_data_size": 0,
                "ndx_resampled_data_size": 0,
                "vix_data_size": 0,
                "executed_trades_size": 0,
                "trade_reports_size": 0,
                "bucket_version_size": 0,
                "bucket_spec_size": 0,
                "last_refresh": None
            }
            
            # Get file sizes
            if status["raw_data_exists"]:
                status["raw_data_size"] = len(pd.read_parquet(self.raw_data_file))
            if status["resampled_data_exists"]:
                status["resampled_data_size"] = len(pd.read_parquet(self.resampled_data_file))
            if status["ndx_data_exists"]:
                status["ndx_data_size"] = len(pd.read_parquet(self.ndx_data_file))
            if status["ndx_resampled_data_exists"]:
                status["ndx_resampled_data_size"] = len(pd.read_parquet(self.ndx_resampled_data_file))
            if status["vix_data_exists"]:
                status["vix_data_size"] = len(pd.read_parquet(self.vix_data_file))
            if status["executed_trades_exists"]:
                status["executed_trades_size"] = len(pd.read_parquet(self.executed_trades_file))
            if status["trade_reports_exists"]:
                status["trade_reports_size"] = len(pd.read_parquet(self.trade_reports_file))
            if status["bucket_version_exists"]:
                status["bucket_version_size"] = len(pd.read_parquet(self.bucket_version_file))
            if status["bucket_spec_exists"]:
                status["bucket_spec_size"] = len(pd.read_parquet(self.bucket_spec_file))
            
            # Get last refresh time from cache status file
            if self.cache_status_file.exists():
                try:
                    with open(self.cache_status_file, 'r') as f:
                        cache_status = json.load(f)
                        status["last_refresh"] = cache_status.get("last_refresh")
                except Exception as e:
                    logger.warning(f"Error reading cache status file: {str(e)}")
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting cache status: {str(e)}")
            return {
                "raw_data_exists": False,
                "resampled_data_exists": False,
                "ndx_data_exists": False,
                "ndx_resampled_data_exists": False,
                "vix_data_exists": False,
                "executed_trades_exists": False,
                "bucket_version_exists": False,
                "bucket_spec_exists": False,
                "raw_data_size": 0,
                "resampled_data_size": 0,
                "ndx_data_size": 0,
                "ndx_resampled_data_size": 0,
                "vix_data_size": 0,
                "executed_trades_size": 0,
                "bucket_version_size": 0,
                "bucket_spec_size": 0,
                "last_refresh": None
            }
    
    def _load_parquet_with_filters(self, file_path: Path, start_date: Optional[Union[str, date, datetime]] = None,
                                  end_date: Optional[Union[str, date, datetime]] = None,
                                  columns: Optional[List[str]] = None,
                                  data_type: str = "data") -> pd.DataFrame:
        """Load Parquet file with optional date filtering and column selection using PyArrow for performance"""
        if not file_path.exists():
            logger.info(f"{data_type.capitalize()} cache file does not exist")
            return pd.DataFrame()

        try:
            # Convert date parameters to pandas Timestamp for consistent filtering
            start_ts = None
            end_ts = None

            if start_date is not None:
                if isinstance(start_date, str):
                    start_ts = pd.to_datetime(start_date)
                elif isinstance(start_date, date):
                    start_ts = pd.Timestamp(start_date)
                elif isinstance(start_date, datetime):
                    start_ts = pd.Timestamp(start_date)
                else:
                    start_ts = pd.to_datetime(start_date)

            if end_date is not None:
                if isinstance(end_date, str):
                    end_ts = pd.to_datetime(end_date)
                elif isinstance(end_date, date):
                    # For end date, include the entire day
                    end_ts = pd.Timestamp(end_date) + pd.Timedelta(days=1) - pd.Timedelta(microseconds=1)
                elif isinstance(end_date, datetime):
                    end_ts = pd.Timestamp(end_date)
                else:
                    end_ts = pd.to_datetime(end_date)

            # Use PyArrow for efficient filtering
            parquet_file = pq.ParquetFile(file_path)

            # Build PyArrow filters for date range
            filters = []
            if start_ts is not None:
                filters.append(('timestamp', '>=', start_ts))
            if end_ts is not None:
                filters.append(('timestamp', '<=', end_ts))

            # Read with filters and column selection
            if filters or columns:
                table = parquet_file.read(filters=filters if filters else None, columns=columns)
                df = table.to_pandas()
            else:
                # No filters, use regular pandas read for simplicity
                df = pd.read_parquet(file_path, columns=columns)

            # Ensure timestamp is the index if it exists as a column
            if 'timestamp' in df.columns and df.index.name != 'timestamp':
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            elif df.index.name == 'timestamp':
                df.index = pd.to_datetime(df.index)

            logger.info(f"Loaded {len(df)} rows of {data_type} from cache" +
                       (f" (filtered from {start_date} to {end_date})" if start_date or end_date else "") +
                       (f" (columns: {columns})" if columns else ""))

            return df

        except Exception as e:
            logger.error(f"Error loading {data_type} from cache with filters: {str(e)}")
            # Fallback to regular pandas read without filters
            try:
                df = pd.read_parquet(file_path, columns=columns)
                logger.info(f"Loaded {len(df)} rows of {data_type} from cache (fallback without filters)")
                return df
            except Exception as fallback_e:
                logger.error(f"Fallback loading also failed: {str(fallback_e)}")
                return pd.DataFrame()

    def _update_cache_status(self):
        """Update cache status file"""
        try:
            status = {
                "last_refresh": datetime.now().isoformat(),
                "cache_dir": str(self.cache_dir)
            }

            with open(self.cache_status_file, 'w') as f:
                json.dump(status, f, indent=2)

            logger.info("Updated cache status file")

        except Exception as e:
            logger.error(f"Error updating cache status: {str(e)}")

    def load_backtest_latest_saved_from_cache(self, start_date: Optional[Union[str, date, datetime]] = None,
                                             end_date: Optional[Union[str, date, datetime]] = None,
                                             columns: Optional[List[str]] = None) -> pd.DataFrame:
        """Load backtest_latest_saved data from cache with optional date filtering and column selection"""
        # If no filters and we have cached data, use it
        if self._cached_data is not None and start_date is None and end_date is None and columns is None:
            logger.info("Using cached backtest_latest_saved data (no disk I/O)")
            return self._cached_data[6]  # backtest_latest_saved_df is seventh in the tuple

        return self._load_parquet_with_filters(self.backtest_latest_saved_file, start_date, end_date, columns, "backtest_latest_saved data")